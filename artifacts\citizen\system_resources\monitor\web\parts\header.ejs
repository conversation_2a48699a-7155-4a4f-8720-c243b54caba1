<!DOCTYPE html>
<html lang="en">

<head>
    <base href="<%= basePath %>">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <meta name="description" content="txAdmin - remotely Manage & Monitor your GTA5 FiveM Server">
    <meta name="author" content="André Tabarra">
    <title>txAdmin</title>
    <!--style-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.0/css/simple-line-icons.min.css" 
        integrity="sha512-yxSQDoat2NPQ9U4mKdYra2YNenVsnRuGxVvyrirmrDKzwOdxKJVqkDEvQ17pL/PJ6B7n2n77Xm6d62bMjCBqCQ==" 
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="<%= resourcePath %>css/coreui.min.css">
    <link rel="stylesheet" href="<%= resourcePath %>css/jquery-confirm.min.css">
    <link rel="stylesheet" href="<%= resourcePath %>css/txAdmin.css">
    <link rel="stylesheet" href="<%= resourcePath %>css/dark.css">
    <link rel="shortcut icon" href="img/favicon_default.png" type="image/png" id="favicon" />
    <style>
        .zapAdContainer {
            padding-left: 1em;
            display: flex;
            color: unset !important;
        }
        .zapLogo {
            height: 26px;
            margin-top: auto;
            margin-bottom: auto;
        } 
        .zapText {
            font-size: 0.85em;
            font-weight: 700;
            padding-left: 8px;
            align-self: center;
            text-transform: initial;
        }
        .zapTextIcon {
            margin-top: -0.25em;
            width: 0.7rem !important;
            height: 0.7rem !important;
            font-size: 0.7rem !important;
        }
    </style>
    <!-- injected consts -->
    <script><%- jsInjection %></script>
</head>

<body class="c-app <%= uiTheme %>">
    <!-- Menu -->
    <div id="sidebar" class="c-sidebar c-sidebar-dark c-sidebar-fixed 
        <%= (isWebInterface ? 'c-sidebar-lg-show' : 'c-sidebar-show c-sidebar-minimized') %>">
        <% if (isWebInterface) { %>
            <div class="c-sidebar-brand d-md-down-none">
                <a href="/">
                    <img class="c-sidebar-brand-full" src="img/txadmin.png" width="118" alt="txAdmin Logo">
                    <img class="c-sidebar-brand-minimized" src="img/tx.png" width="30" height="30" alt="txAdmin Logo">
                </a>
            </div>
        <% } %>
        <ul class="c-sidebar-nav">
            <li class="c-sidebar-nav-title font-size-larger">
                Profile: <%= serverProfile %>
            </li>

            <!-- <li class="c-sidebar-nav-item c-sidebar-nav-dropdown">
                <a class="c-sidebar-nav-link c-sidebar-nav-dropdown-toggle" href="#">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons-full.svg#cil-layers"></use>
                    </svg> Dropdown example
                </a>
                <ul class="c-sidebar-nav-dropdown-items">
                    <li class="c-sidebar-nav-item">
                        <a class="c-sidebar-nav-link" href="console">
                            <svg class="c-sidebar-nav-icon">
                                <use href="img/coreui_icons.svg#cil-terminal"></use>
                            </svg> option 1
                        </a>
                    </li>
                </ul>
            </li> -->

            <!-- <span class="badge badge-info">NEW</span> -->

            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="<%= basePath %>">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-speedometer"></use>
                    </svg> Dashboard
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="player/list">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-people"></use>
                    </svg> Players
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="whitelist">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-user-follow"></use>
                    </svg> Whitelist
                </a>
            </li>
            <!-- <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="insights">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons-full.svg#cil-lightbulb"></use>
                    </svg> Insights
                </a>
            </li> -->
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="console">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-terminal"></use>
                    </svg> Live Console
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="resources">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-3d"></use>
                    </svg> Resources
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="serverLog">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-excerpt"></use>
                    </svg> Server Log
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="cfgEditor">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-pencil"></use>
                    </svg> CFG Editor
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="diagnostics">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-chart-pie"></use>
                    </svg> Diagnostics
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="adminManager">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-contact"></use>
                    </svg> Admin Manager
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="systemLog">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-notes"></use>
                    </svg> System Logs
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="settings">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-settings"></use>
                    </svg> Settings
                </a>
            </li>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link" href="masterActions">
                    <svg class="c-sidebar-nav-icon">
                        <use href="img/coreui_icons.svg#cil-bolt"></use>
                    </svg> Master Actions
                </a>
            </li>
            <% if (showAdvanced) { %>
            <li class="c-sidebar-nav-item">
                <a class="c-sidebar-nav-link text-warning" href="advanced">
                    <svg class="c-sidebar-nav-icon text-warning">
                        <use href="img/coreui_icons.svg#cil-fire"></use>
                    </svg> Advanced
                </a>
            </li>
            <% } %>

            <!-- Sidebar footer -->
            <% if (isWebInterface) { %>
                <li class="c-sidebar-nav-divider"></li>
                <li class="c-sidebar-nav-title mt-auto">Host System Usage</li>
                <li class="c-sidebar-nav-item px-3 d-compact-none c-d-minimized-none">
                    <div class="text-uppercase mb-1">
                        <small>
                            <b>CPU Usage</b>
                        </small>
                    </div>
                    <div class="progress progress-xs">
                        <div class="progress-bar bg-info" role="progressbar" style="width: 0%" aria-valuenow="0"
                            aria-valuemin="0" aria-valuemax="100" id="hostusage-cpu-bar"></div>
                    </div>
                    <small class="text-muted" id="hostusage-cpu-text">loading...</small>
                </li>
                <li class="c-sidebar-nav-item px-3 d-compact-none c-d-minimized-none">
                    <div class="text-uppercase mb-1">
                        <small>
                            <b>Memory Usage</b>
                        </small>
                    </div>
                    <div class="progress progress-xs">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: 0%" aria-valuenow="0"
                            aria-valuemin="0" aria-valuemax="100" id="hostusage-memory-bar"></div>
                    </div>
                    <small class="text-muted" id="hostusage-memory-text">loading...</small>
                </li>
            <% } %>

            <!-- Zap Hosting Ad - Mobile -->
            <% if (dynamicAd) { %>
                <li class="c-sidebar-nav-title d-lg-none" style="background-color: rgba(0, 0, 0, 0.178);">
                    <a class="zapAdContainer" href="<%= dynamicAd.linkMobile %>" target="_blank" rel="noopener noreferrer">
                        <img class="zapLogo" alt="zap hosting" src="<%= dynamicAd.logoDarkMode %>">
                        <div class="zapText text-light">
                            <%- dynamicAd.text %>
                            <svg class="c-icon zapTextIcon">
                                <use href="img/coreui_icons.svg#cil-external-link"></use>
                            </svg>
                        </div>
                    </a>
                </li>
            <% } %>

        </ul>
        <button class="c-sidebar-minimizer c-class-toggler" type="button" data-target="_parent"
            data-class="c-sidebar-minimized"></button>
    </div>

    <!-- Player List -->
    <% if (isWebInterface) { %>
        <div class="c-sidebar c-sidebar-md c-sidebar-light c-sidebar-fixed 
            c-sidebar-right c-sidebar-xl-show playerlist-sidebar" id="plist">
            <div class="tab-pane" role="tabpanel">
                <div class="list-group list-group-accent">
                    <div class="list-group-item list-group-item-accent-secondary playerlist-header plheader">
                        <span class="plheader-label font-weight-bold text-muted text-uppercase">
                            Players:
                        </span>
                        <span class="plheader-bignum float-right" id="plist-count">--</span>
                    </div>
                    <div class="list-group-item list-group-item-accent-secondary playerlist-search">
                        <input class="form-control form-control-sm text-center" type="text"
                            placeholder="Search" id="plist-search">
                    </div>
                    <div class="playerlist thin-scroll" id="playerlist">
                      <div class="list-group-item text-center p-3 font-weight-bold" id="playerlist-message">
                        loading...
                      </div>
                    </div>
                </div>
            </div>
        </div>
    <% } %>

    <!-- Center Content -->
    <div class="c-wrapper c-fixed-components">
        <!-- Header -->
        <% if (isWebInterface) { %>
            <header class="c-header c-header-light c-header-fixed c-header-with-subheader">
                <!-- Zap Hosting Ad - Desktop -->
                <% if (dynamicAd) { %>
                    <a class="zapAdContainer d-md-down-none" href="<%= dynamicAd.linkDesktop %>" target="_blank" rel="noopener noreferrer">
                        <img class="zapLogo show-dark-mode" alt="zap hosting" src="<%= dynamicAd.logoDarkMode %>">
                        <img class="zapLogo show-light-mode" alt="zap hosting" src="<%= dynamicAd.logoLightMode %>">
                        <div class="zapText attentionText">
                            <%- dynamicAd.text %>
                            <svg class="c-icon zapTextIcon">
                                <use href="img/coreui_icons.svg#cil-external-link"></use>
                            </svg>
                        </div>
                    </a>
                <% } %>

                <button class="c-header-toggler c-class-toggler d-lg-none mfe-auto" type="button" data-target="#sidebar"
                    data-class="c-sidebar-show">
                    <svg class="c-icon c-icon-lg">
                        <use href="img/coreui_icons.svg#cil-menu"></use>
                    </svg>
                </button>
                <a class="c-header-brand d-lg-none" href="/">
                    <img class="c-sidebar-brand-minimized" src="img/tx.png" width="30" height="30" alt="txAdmin Logo">
                </a>
                <ul class="c-header-nav ml-auto mr-4">
                    <li class="c-header-nav-item mx-1">
                        <div id="darkToggleArea" title="Have you tried our Dark Mode yet? 😎">
                            <svg class="c-sidebar-nav-icon d-none" id="darkToggleLight">
                                <use href="img/coreui_icons.svg#cil-sun"></use>
                            </svg>
                            <svg class="c-sidebar-nav-icon" id="darkToggleDark">
                                <use href="img/coreui_icons.svg#cil-moon"></use>
                            </svg>
                        </div>
                    </li>

                    <li class="c-header-nav-item dropdown">
                        <a class="c-header-nav-link" data-toggle="dropdown" href="#" role="button" aria-haspopup="true"
                            aria-expanded="false">
                            <span class="d-md-down-none pr-2"><%= adminUsername %> </span>
                            <div class="c-avatar">
                                <img class="c-avatar-img profile-pic" src="<%= profilePicture %>" alt="<%= adminUsername %>">
                            </div>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right pt-0">
                            <div class="dropdown-header py-2">
                                <strong>Account</strong>
                            </div>
                            <a class="dropdown-item" href="" onclick="changeOwnPasswordModal(); return false;">
                                <svg class="c-icon mr-2">
                                    <use href="img/coreui_icons.svg#cil-lock-locked"></use>
                                </svg> Change password
                            </a>
                            <a class="dropdown-item" href="auth?logout">
                                <svg class="c-icon mr-2">
                                    <use href="img/coreui_icons.svg#cil-account-logout"></use>
                                </svg> Logout
                            </a>
                        </div>
                    </li>
                </ul>
                <button class="c-header-toggler c-class-toggler d-xl-none" type="button" data-target="#plist"
                    data-class="playerlist-sidebar-max">
                    <svg class="c-icon c-icon-lg">
                        <use href="img/coreui_icons.svg#cil-menu"></use>
                    </svg>
                </button>
            </header>
        <% } %> <%# end if(isWebInterface) %>

        <!-- Actual Body -->
        <div class="c-body">
            <main class="c-main thin-scroll" id="mainbody">
                <div class="container-fluid">
                    <div class="fade-in">
<!-- Begin Page Content -->
