@charset "UTF-8";
/*!
 * CoreUI - HTML, CSS, and JavaScript UI Components Library
 * @version v3.0.0
 * @link https://coreui.io/
 * Copyright (c) 2020 creativeLabs <PERSON><PERSON><PERSON>
 * License MIT  (https://coreui.io/license/)
 */
:root {
  --primary: #321fdb;
  --secondary: #ced2d8;
  --success: #2eb85c;
  --info: #39f;
  --warning: #f9b115;
  --danger: #e55353;
  --light: #ebedef;
  --dark: #636f83;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-monospace: SFMono-Regular, <PERSON><PERSON>, Monaco, <PERSON>, "Liberation Mono", "Courier New", monospace;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 21, 0);
}

article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

body,.c-app {
  color: #3c4b64;
  background-color: #ebedef;
}

.c-app {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-height: 100vh;
}

[tabindex="-1"]:focus:not(:focus-visible) {
  outline: 0 !important;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 21, 0.2);
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
}

address {
  font-style: normal;
  line-height: inherit;
}

address,ol,
ul,
dl {
  margin-bottom: 1rem;
}

ol,
ul,
dl {
  margin-top: 0;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: .5rem;
}

html:not([dir="rtl"]) dd {
  margin-left: 0;
}

*[dir="rtl"] dd {
  margin-right: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

a {
  text-decoration: none;
  background-color: transparent;
}

a,a:hover {
  color: #321fdb;
}

a:hover {
  text-decoration: underline;
}

a:not([href]),a:not([href]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  display: block;
  font-size: 87.5%;
  color: #4f5d73;
}

figure {
  margin: 0 0 1rem;
}

img {
  border-style: none;
}

img,svg {
  vertical-align: middle;
}

svg {
  overflow: hidden;
}

table {
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #768192;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: inherit;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button {
  border-radius: 0;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

select {
  word-wrap: normal;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button:not(:disabled),
[type="button"]:not(:disabled),
[type="reset"]:not(:disabled),
[type="submit"]:not(:disabled) {
  cursor: pointer;
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox;
}

textarea {
  overflow: auto;
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

progress {
  vertical-align: baseline;
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

summary {
  display: list-item;
  cursor: pointer;
}

template {
  display: none;
}

[hidden] {
  display: none !important;
}

.ps {
  overflow: hidden !important;
  -ms-touch-action: auto;
  touch-action: auto;
  -ms-overflow-style: none;
  overflow-anchor: none;
}

.ps__rail-x {
  bottom: 0;
  height: 15px;
}

.ps__rail-x,.ps__rail-y {
  position: absolute;
  display: none;
  opacity: 0;
  transition: background-color .2s linear, opacity .2s linear;
}

.ps__rail-y {
  width: 15px;
}

html:not([dir="rtl"]) .ps__rail-y {
  right: 0;
}

*[dir="rtl"] .ps__rail-y {
  left: 0;
}

.ps--active-x > .ps__rail-x,
.ps--active-y > .ps__rail-y {
  display: block;
  background-color: transparent;
}

.ps:hover > .ps__rail-x,
.ps:hover > .ps__rail-y,
.ps--focus > .ps__rail-x,
.ps--focus > .ps__rail-y,
.ps--scrolling-x > .ps__rail-x,
.ps--scrolling-y > .ps__rail-y {
  opacity: .6;
}

.ps__rail-x:hover,
.ps__rail-y:hover,
.ps__rail-x:focus,
.ps__rail-y:focus {
  background-color: #eee;
  opacity: .9;
}

/*
 * Scrollbar thumb styles
 */
.ps__thumb-x {
  bottom: 2px;
  height: 6px;
  transition: background-color .2s linear, height .2s ease-in-out;
}

.ps__thumb-x,.ps__thumb-y {
  position: absolute;
  background-color: #aaa;
  border-radius: 6px;
}

.ps__thumb-y {
  width: 6px;
  transition: background-color .2s linear, width .2s ease-in-out;
}

html:not([dir="rtl"]) .ps__thumb-y {
  right: 2px;
}

*[dir="rtl"] .ps__thumb-y {
  left: 2px;
}

.ps__rail-x:hover > .ps__thumb-x,
.ps__rail-x:focus > .ps__thumb-x {
  height: 11px;
  background-color: #999;
}

.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y {
  width: 11px;
  background-color: #999;
}

@supports (-ms-overflow-style: none) {
  .ps {
    overflow: auto !important;
  }
}

@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .ps {
    overflow: auto !important;
  }
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
}

html:not([dir="rtl"]) .alert-dismissible {
  padding-right: 3.8125rem;
}

*[dir="rtl"] .alert-dismissible {
  padding-left: 3.8125rem;
}

.alert-dismissible .close {
  position: absolute;
  top: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
}

html:not([dir="rtl"]) .alert-dismissible .close {
  right: 0;
}

*[dir="rtl"] .alert-dismissible .close {
  left: 0;
}

.alert-primary {
  color: #1a107c;
  background-color: #d6d2f8;
  border-color: #c6c0f5;
}

.alert-primary hr {
  border-top-color: #b2aaf2;
}

.alert-primary .alert-link {
  color: #110a4f;
}

.alert-secondary {
  color: #6b6d7a;
  background-color: #f5f6f7;
  border-color: #f1f2f4;
}

.alert-secondary hr {
  border-top-color: #e3e5e9;
}

.alert-secondary .alert-link {
  color: #53555f;
}

.alert-success {
  color: #18603a;
  background-color: #d5f1de;
  border-color: #c4ebd1;
}

.alert-success hr {
  border-top-color: #b1e5c2;
}

.alert-success .alert-link {
  color: #0e3721;
}

.alert-info {
  color: #1b508f;
  background-color: #d6ebff;
  border-color: #c6e2ff;
}

.alert-info hr {
  border-top-color: #add5ff;
}

.alert-info .alert-link {
  color: #133864;
}

.alert-warning {
  color: #815c15;
  background-color: #feefd0;
  border-color: #fde9bd;
}

.alert-warning hr {
  border-top-color: #fce1a4;
}

.alert-warning .alert-link {
  color: #553d0e;
}

.alert-danger {
  color: #772b35;
  background-color: #fadddd;
  border-color: #f8cfcf;
}

.alert-danger hr {
  border-top-color: #f5b9b9;
}

.alert-danger .alert-link {
  color: #521d24;
}

.alert-light {
  color: #7a7b86;
  background-color: #fbfbfc;
  border-color: #f9fafb;
}

.alert-light hr {
  border-top-color: #eaedf1;
}

.alert-light .alert-link {
  color: #62626b;
}

.alert-dark {
  color: #333a4e;
  background-color: #e0e2e6;
  border-color: #d3d7dc;
}

.alert-dark hr {
  border-top-color: #c5cad1;
}

.alert-dark .alert-link {
  color: #1f232f;
}

.c-avatar {
  position: relative;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 50em;
  width: 36px;
  height: 36px;
  font-size: 14.4px;
}

.c-avatar .c-avatar-status {
  width: 10px;
  height: 10px;
}

.c-avatar-img {
  width: 100%;
  height: auto;
  border-radius: 50em;
}

.c-avatar-status {
  position: absolute;
  bottom: 0;
  display: block;
  border: 1px solid #fff;
  border-radius: 50em;
}

html:not([dir="rtl"]) .c-avatar-status {
  right: 0;
}

*[dir="rtl"] .c-avatar-status {
  left: 0;
}

.c-avatar-sm {
  width: 24px;
  height: 24px;
  font-size: 9.6px;
}

.c-avatar-sm .c-avatar-status {
  width: 8px;
  height: 8px;
}

.c-avatar-lg {
  width: 48px;
  height: 48px;
  font-size: 19.2px;
}

.c-avatar-lg .c-avatar-status {
  width: 12px;
  height: 12px;
}

.c-avatar-xl {
  width: 64px;
  height: 64px;
  font-size: 25.6px;
}

.c-avatar-xl .c-avatar-status {
  width: 14px;
  height: 14px;
}

.c-avatars-stack {
  display: -ms-flexbox;
  display: flex;
}

.c-avatars-stack .c-avatar {
  margin-right: -18px;
  transition: margin-right 0.25s;
}

.c-avatars-stack .c-avatar:hover {
  margin-right: 0;
}

.c-avatars-stack .c-avatar-sm {
  margin-right: -12px;
}

.c-avatars-stack .c-avatar-lg {
  margin-right: -24px;
}

.c-avatars-stack .c-avatar-xl {
  margin-right: -32px;
}

.c-avatar-rounded {
  border-radius: 0.25rem;
}

.c-avatar-square {
  border-radius: 0;
}

.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .badge {
    transition: none;
  }
}

a.badge:hover, a.badge:focus {
  text-decoration: none;
}

.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}

.badge-primary {
  color: #fff;
  background-color: #321fdb;
}

a.badge-primary:hover, a.badge-primary:focus {
  color: #fff;
  background-color: #2819ae;
}

a.badge-primary:focus, a.badge-primary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.5);
}

.badge-secondary {
  color: #4f5d73;
  background-color: #ced2d8;
}

a.badge-secondary:hover, a.badge-secondary:focus {
  color: #4f5d73;
  background-color: #b2b8c1;
}

a.badge-secondary:focus, a.badge-secondary.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(206, 210, 216, 0.5);
}

.badge-success {
  color: #fff;
  background-color: #2eb85c;
}

a.badge-success:hover, a.badge-success:focus {
  color: #fff;
  background-color: #248f48;
}

a.badge-success:focus, a.badge-success.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.5);
}

.badge-info {
  color: #fff;
  background-color: #39f;
}

a.badge-info:hover, a.badge-info:focus {
  color: #fff;
  background-color: #0080ff;
}

a.badge-info:focus, a.badge-info.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(51, 153, 255, 0.5);
}

.badge-warning {
  color: #4f5d73;
  background-color: #f9b115;
}

a.badge-warning:hover, a.badge-warning:focus {
  color: #4f5d73;
  background-color: #d69405;
}

a.badge-warning:focus, a.badge-warning.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(249, 177, 21, 0.5);
}

.badge-danger {
  color: #fff;
  background-color: #e55353;
}

a.badge-danger:hover, a.badge-danger:focus {
  color: #fff;
  background-color: #de2727;
}

a.badge-danger:focus, a.badge-danger.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.5);
}

.badge-light {
  color: #4f5d73;
  background-color: #ebedef;
}

a.badge-light:hover, a.badge-light:focus {
  color: #4f5d73;
  background-color: #cfd4d8;
}

a.badge-light:focus, a.badge-light.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(235, 237, 239, 0.5);
}

.badge-dark {
  color: #fff;
  background-color: #636f83;
}

a.badge-dark:hover, a.badge-dark:focus {
  color: #fff;
  background-color: #4d5666;
}

a.badge-dark:focus, a.badge-dark.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(99, 111, 131, 0.5);
}

html:not([dir="rtl"]) .breadcrumb-menu {
  margin-left: auto;
  margin-right: auto;
}

.breadcrumb-menu::before {
  display: none;
}

.breadcrumb-menu .btn-group,.breadcrumb-menu .btn {
  vertical-align: top;
}

.breadcrumb-menu .btn {
  padding: 0 0.75rem;
  color: #768192;
  border: 0;
}

.breadcrumb-menu .btn:hover, .breadcrumb-menu .btn.active,.breadcrumb-menu .show .btn {
  color: #4f5d73;
  background: transparent;
}

.breadcrumb-menu .dropdown-menu {
  min-width: 180px;
  line-height: 1.5;
}

.breadcrumb {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  list-style: none;
  border-radius: 0;
  border-bottom: 1px solid;
  background-color: transparent;
  border-color: #d8dbe0;
}

html:not([dir="rtl"]) .breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}

*[dir="rtl"] .breadcrumb-item + .breadcrumb-item {
  padding-right: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  color: #8a93a2;
  content: "/";
}

html:not([dir="rtl"]) .breadcrumb-item + .breadcrumb-item::before {
  padding-right: 0.5rem;
}

*[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
  padding-left: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item:hover::before {
  text-decoration: underline;
  text-decoration: none;
}

.breadcrumb-item.active {
  color: #8a93a2;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}

.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.btn-group > .btn:hover,
.btn-group-vertical > .btn:hover,.btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}

.btn-toolbar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.btn-toolbar .input-group {
  width: auto;
}

html:not([dir="rtl"]) .btn-group > .btn:not(:first-child), html:not([dir="rtl"])
.btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}

*[dir="rtl"] .btn-group > .btn:not(:first-child), *[dir="rtl"]
.btn-group > .btn-group:not(:first-child) {
  margin-right: -1px;
}

html:not([dir="rtl"]) .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
html:not([dir="rtl"]) .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

html:not([dir="rtl"]) .btn-group > .btn:not(:first-child),
html:not([dir="rtl"]) .btn-group > .btn-group:not(:first-child) > .btn,*[dir="rtl"] .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
*[dir="rtl"] .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

*[dir="rtl"] .btn-group > .btn:not(:first-child),
*[dir="rtl"] .btn-group > .btn-group:not(:first-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}

html:not([dir="rtl"]) .dropdown-toggle-split::after, html:not([dir="rtl"])
.dropup .dropdown-toggle-split::after, html:not([dir="rtl"])
.dropright .dropdown-toggle-split::after {
  margin-left: 0;
}

*[dir="rtl"] .dropdown-toggle-split::after, *[dir="rtl"]
.dropup .dropdown-toggle-split::after, *[dir="rtl"]
.dropright .dropdown-toggle-split::after,html:not([dir="rtl"]) .dropleft .dropdown-toggle-split::before {
  margin-right: 0;
}

*[dir="rtl"] .dropleft .dropdown-toggle-split::before {
  margin-left: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn-group-vertical {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: center;
  justify-content: center;
}

.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}

.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}

.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.btn-group-toggle > .btn,
.btn-group-toggle > .btn-group > .btn {
  margin-bottom: 0;
}

.btn-group-toggle > .btn input[type="radio"],
.btn-group-toggle > .btn input[type="checkbox"],
.btn-group-toggle > .btn-group > .btn input[type="radio"],
.btn-group-toggle > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.btn {
  display: inline-block;
  font-weight: 400;
  color: #4f5d73;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn i,
.btn .c-icon {
  width: 0.875rem;
  height: 0.875rem;
  margin: 0.21875rem 0;
  height: 0.875rem;
  margin: 0.21875rem 0;
}

@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}

.btn:hover {
  color: #4f5d73;
  text-decoration: none;
}

.btn:focus, .btn.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

.btn.disabled, .btn:disabled {
  opacity: 0.65;
}

a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none;
}

.btn-primary {
  color: #fff;
  background-color: #321fdb;
  border-color: #321fdb;
}

.btn-primary:hover,.btn-primary:focus, .btn-primary.focus {
  color: #fff;
  background-color: #2a1ab9;
  border-color: #2819ae;
}

.btn-primary:focus, .btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(81, 65, 224, 0.5);
}

.btn-primary.disabled, .btn-primary:disabled {
  color: #fff;
  background-color: #321fdb;
  border-color: #321fdb;
}

.btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #2819ae;
  border-color: #2517a3;
}

.btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(81, 65, 224, 0.5);
}

.btn-secondary {
  color: #4f5d73;
  background-color: #ced2d8;
  border-color: #ced2d8;
}

.btn-secondary:hover,.btn-secondary:focus, .btn-secondary.focus {
  color: #4f5d73;
  background-color: #b9bec7;
  border-color: #b2b8c1;
}

.btn-secondary:focus, .btn-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(187, 192, 201, 0.5);
}

.btn-secondary.disabled, .btn-secondary:disabled {
  color: #4f5d73;
  background-color: #ced2d8;
  border-color: #ced2d8;
}

.btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active,
.show > .btn-secondary.dropdown-toggle {
  color: #4f5d73;
  background-color: #b2b8c1;
  border-color: #abb1bc;
}

.btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(187, 192, 201, 0.5);
}

.btn-success {
  color: #fff;
  background-color: #2eb85c;
  border-color: #2eb85c;
}

.btn-success:hover,.btn-success:focus, .btn-success.focus {
  color: #fff;
  background-color: #26994d;
  border-color: #248f48;
}

.btn-success:focus, .btn-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(77, 195, 116, 0.5);
}

.btn-success.disabled, .btn-success:disabled {
  color: #fff;
  background-color: #2eb85c;
  border-color: #2eb85c;
}

.btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active,
.show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #248f48;
  border-color: #218543;
}

.btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(77, 195, 116, 0.5);
}

.btn-info {
  color: #fff;
  background-color: #39f;
  border-color: #39f;
}

.btn-info:hover,.btn-info:focus, .btn-info.focus {
  color: #fff;
  background-color: #0d86ff;
  border-color: #0080ff;
}

.btn-info:focus, .btn-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 168, 255, 0.5);
}

.btn-info.disabled, .btn-info:disabled {
  color: #fff;
  background-color: #39f;
  border-color: #39f;
}

.btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active,
.show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #0080ff;
  border-color: #0079f2;
}

.btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 168, 255, 0.5);
}

.btn-warning {
  color: #4f5d73;
  background-color: #f9b115;
  border-color: #f9b115;
}

.btn-warning:hover,.btn-warning:focus, .btn-warning.focus {
  color: #4f5d73;
  background-color: #e29c06;
  border-color: #d69405;
}

.btn-warning:focus, .btn-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(224, 164, 35, 0.5);
}

.btn-warning.disabled, .btn-warning:disabled {
  color: #4f5d73;
  background-color: #f9b115;
  border-color: #f9b115;
}

.btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active,
.show > .btn-warning.dropdown-toggle {
  color: #4f5d73;
  background-color: #d69405;
  border-color: #c98b05;
}

.btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(224, 164, 35, 0.5);
}

.btn-danger {
  color: #fff;
  background-color: #e55353;
  border-color: #e55353;
}

.btn-danger:hover,.btn-danger:focus, .btn-danger.focus {
  color: #fff;
  background-color: #e03232;
  border-color: #de2727;
}

.btn-danger:focus, .btn-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(233, 109, 109, 0.5);
}

.btn-danger.disabled, .btn-danger:disabled {
  color: #fff;
  background-color: #e55353;
  border-color: #e55353;
}

.btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active,
.show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #de2727;
  border-color: #d82121;
}

.btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus,
.show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(233, 109, 109, 0.5);
}

.btn-light {
  color: #4f5d73;
  background-color: #ebedef;
  border-color: #ebedef;
}

.btn-light:hover,.btn-light:focus, .btn-light.focus {
  color: #4f5d73;
  background-color: #d6dade;
  border-color: #cfd4d8;
}

.btn-light:focus, .btn-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(212, 215, 220, 0.5);
}

.btn-light.disabled, .btn-light:disabled {
  color: #4f5d73;
  background-color: #ebedef;
  border-color: #ebedef;
}

.btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active,
.show > .btn-light.dropdown-toggle {
  color: #4f5d73;
  background-color: #cfd4d8;
  border-color: #c8cdd3;
}

.btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(212, 215, 220, 0.5);
}

.btn-dark {
  color: #fff;
  background-color: #636f83;
  border-color: #636f83;
}

.btn-dark:hover,.btn-dark:focus, .btn-dark.focus {
  color: #fff;
  background-color: #535d6d;
  border-color: #4d5666;
}

.btn-dark:focus, .btn-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(122, 133, 150, 0.5);
}

.btn-dark.disabled, .btn-dark:disabled {
  color: #fff;
  background-color: #636f83;
  border-color: #636f83;
}

.btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active,
.show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #4d5666;
  border-color: #48505f;
}

.btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus,
.show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(122, 133, 150, 0.5);
}

.btn-transparent {
  color: rgba(255, 255, 255, 0.8);
}

.btn-transparent:hover {
  color: white;
}

.btn-outline-primary {
  color: #321fdb;
  border-color: #321fdb;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #321fdb;
  border-color: #321fdb;
}

.btn-outline-primary:focus, .btn-outline-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.5);
}

.btn-outline-primary.disabled, .btn-outline-primary:disabled {
  color: #321fdb;
  background-color: transparent;
}

.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #321fdb;
  border-color: #321fdb;
}

.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.5);
}

.btn-outline-secondary {
  color: #ced2d8;
  border-color: #ced2d8;
}

.btn-outline-secondary:hover {
  color: #4f5d73;
  background-color: #ced2d8;
  border-color: #ced2d8;
}

.btn-outline-secondary:focus, .btn-outline-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(206, 210, 216, 0.5);
}

.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
  color: #ced2d8;
  background-color: transparent;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active,
.show > .btn-outline-secondary.dropdown-toggle {
  color: #4f5d73;
  background-color: #ced2d8;
  border-color: #ced2d8;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(206, 210, 216, 0.5);
}

.btn-outline-success {
  color: #2eb85c;
  border-color: #2eb85c;
}

.btn-outline-success:hover {
  color: #fff;
  background-color: #2eb85c;
  border-color: #2eb85c;
}

.btn-outline-success:focus, .btn-outline-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.5);
}

.btn-outline-success.disabled, .btn-outline-success:disabled {
  color: #2eb85c;
  background-color: transparent;
}

.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active,
.show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #2eb85c;
  border-color: #2eb85c;
}

.btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.5);
}

.btn-outline-info {
  color: #39f;
  border-color: #39f;
}

.btn-outline-info:hover {
  color: #fff;
  background-color: #39f;
  border-color: #39f;
}

.btn-outline-info:focus, .btn-outline-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(51, 153, 255, 0.5);
}

.btn-outline-info.disabled, .btn-outline-info:disabled {
  color: #39f;
  background-color: transparent;
}

.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active,
.show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #39f;
  border-color: #39f;
}

.btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(51, 153, 255, 0.5);
}

.btn-outline-warning {
  color: #f9b115;
  border-color: #f9b115;
}

.btn-outline-warning:hover {
  color: #4f5d73;
  background-color: #f9b115;
  border-color: #f9b115;
}

.btn-outline-warning:focus, .btn-outline-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(249, 177, 21, 0.5);
}

.btn-outline-warning.disabled, .btn-outline-warning:disabled {
  color: #f9b115;
  background-color: transparent;
}

.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active,
.show > .btn-outline-warning.dropdown-toggle {
  color: #4f5d73;
  background-color: #f9b115;
  border-color: #f9b115;
}

.btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(249, 177, 21, 0.5);
}

.btn-outline-danger {
  color: #e55353;
  border-color: #e55353;
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #e55353;
  border-color: #e55353;
}

.btn-outline-danger:focus, .btn-outline-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.5);
}

.btn-outline-danger.disabled, .btn-outline-danger:disabled {
  color: #e55353;
  background-color: transparent;
}

.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active,
.show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #e55353;
  border-color: #e55353;
}

.btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.5);
}

.btn-outline-light {
  color: #ebedef;
  border-color: #ebedef;
}

.btn-outline-light:hover {
  color: #4f5d73;
  background-color: #ebedef;
  border-color: #ebedef;
}

.btn-outline-light:focus, .btn-outline-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(235, 237, 239, 0.5);
}

.btn-outline-light.disabled, .btn-outline-light:disabled {
  color: #ebedef;
  background-color: transparent;
}

.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active,
.show > .btn-outline-light.dropdown-toggle {
  color: #4f5d73;
  background-color: #ebedef;
  border-color: #ebedef;
}

.btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(235, 237, 239, 0.5);
}

.btn-outline-dark {
  color: #636f83;
  border-color: #636f83;
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #636f83;
  border-color: #636f83;
}

.btn-outline-dark:focus, .btn-outline-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 111, 131, 0.5);
}

.btn-outline-dark.disabled, .btn-outline-dark:disabled {
  color: #636f83;
  background-color: transparent;
}

.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active,
.show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #636f83;
  border-color: #636f83;
}

.btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 111, 131, 0.5);
}

.btn-link {
  font-weight: 400;
  color: #321fdb;
  text-decoration: none;
}

.btn-link:hover {
  color: #231698;
  text-decoration: underline;
}

.btn-link:focus, .btn-link.focus {
  text-decoration: underline;
  box-shadow: none;
}

.btn-link:disabled, .btn-link.disabled {
  color: #8a93a2;
  pointer-events: none;
}

.btn-lg, .btn-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.btn-lg i, .btn-group-lg > .btn i,
.btn-lg .c-icon,
.btn-group-lg > .btn .c-icon {
  width: 1.09375rem;
  height: 1.09375rem;
  margin: 0.2734375rem 0;
}

.btn-sm, .btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.btn-sm i, .btn-group-sm > .btn i,
.btn-sm .c-icon,
.btn-group-sm > .btn .c-icon {
  width: 0.765625rem;
  height: 0.765625rem;
  margin: 0.19140625rem 0;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-block + .btn-block {
  margin-top: 0.5rem;
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%;
}

.btn-pill {
  border-radius: 50em;
}

.btn-square {
  border-radius: 0;
}

.btn-ghost-primary {
  color: #321fdb;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}

.btn-ghost-primary:hover {
  color: #fff;
  background-color: #321fdb;
  border-color: #321fdb;
}

.btn-ghost-primary:focus, .btn-ghost-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.5);
}

.btn-ghost-primary.disabled, .btn-ghost-primary:disabled {
  color: #321fdb;
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost-primary:not(:disabled):not(.disabled):active, .btn-ghost-primary:not(:disabled):not(.disabled).active,
.show > .btn-ghost-primary.dropdown-toggle {
  color: #fff;
  background-color: #321fdb;
  border-color: #321fdb;
}

.btn-ghost-primary:not(:disabled):not(.disabled):active:focus, .btn-ghost-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.5);
}

.btn-ghost-secondary {
  color: #ced2d8;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}

.btn-ghost-secondary:hover {
  color: #4f5d73;
  background-color: #ced2d8;
  border-color: #ced2d8;
}

.btn-ghost-secondary:focus, .btn-ghost-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(206, 210, 216, 0.5);
}

.btn-ghost-secondary.disabled, .btn-ghost-secondary:disabled {
  color: #ced2d8;
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost-secondary:not(:disabled):not(.disabled):active, .btn-ghost-secondary:not(:disabled):not(.disabled).active,
.show > .btn-ghost-secondary.dropdown-toggle {
  color: #4f5d73;
  background-color: #ced2d8;
  border-color: #ced2d8;
}

.btn-ghost-secondary:not(:disabled):not(.disabled):active:focus, .btn-ghost-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(206, 210, 216, 0.5);
}

.btn-ghost-success {
  color: #2eb85c;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}

.btn-ghost-success:hover {
  color: #fff;
  background-color: #2eb85c;
  border-color: #2eb85c;
}

.btn-ghost-success:focus, .btn-ghost-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.5);
}

.btn-ghost-success.disabled, .btn-ghost-success:disabled {
  color: #2eb85c;
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost-success:not(:disabled):not(.disabled):active, .btn-ghost-success:not(:disabled):not(.disabled).active,
.show > .btn-ghost-success.dropdown-toggle {
  color: #fff;
  background-color: #2eb85c;
  border-color: #2eb85c;
}

.btn-ghost-success:not(:disabled):not(.disabled):active:focus, .btn-ghost-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.5);
}

.btn-ghost-info {
  color: #39f;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}

.btn-ghost-info:hover {
  color: #fff;
  background-color: #39f;
  border-color: #39f;
}

.btn-ghost-info:focus, .btn-ghost-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(51, 153, 255, 0.5);
}

.btn-ghost-info.disabled, .btn-ghost-info:disabled {
  color: #39f;
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost-info:not(:disabled):not(.disabled):active, .btn-ghost-info:not(:disabled):not(.disabled).active,
.show > .btn-ghost-info.dropdown-toggle {
  color: #fff;
  background-color: #39f;
  border-color: #39f;
}

.btn-ghost-info:not(:disabled):not(.disabled):active:focus, .btn-ghost-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(51, 153, 255, 0.5);
}

.btn-ghost-warning {
  color: #f9b115;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}

.btn-ghost-warning:hover {
  color: #4f5d73;
  background-color: #f9b115;
  border-color: #f9b115;
}

.btn-ghost-warning:focus, .btn-ghost-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(249, 177, 21, 0.5);
}

.btn-ghost-warning.disabled, .btn-ghost-warning:disabled {
  color: #f9b115;
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost-warning:not(:disabled):not(.disabled):active, .btn-ghost-warning:not(:disabled):not(.disabled).active,
.show > .btn-ghost-warning.dropdown-toggle {
  color: #4f5d73;
  background-color: #f9b115;
  border-color: #f9b115;
}

.btn-ghost-warning:not(:disabled):not(.disabled):active:focus, .btn-ghost-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(249, 177, 21, 0.5);
}

.btn-ghost-danger {
  color: #e55353;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}

.btn-ghost-danger:hover {
  color: #fff;
  background-color: #e55353;
  border-color: #e55353;
}

.btn-ghost-danger:focus, .btn-ghost-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.5);
}

.btn-ghost-danger.disabled, .btn-ghost-danger:disabled {
  color: #e55353;
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost-danger:not(:disabled):not(.disabled):active, .btn-ghost-danger:not(:disabled):not(.disabled).active,
.show > .btn-ghost-danger.dropdown-toggle {
  color: #fff;
  background-color: #e55353;
  border-color: #e55353;
}

.btn-ghost-danger:not(:disabled):not(.disabled):active:focus, .btn-ghost-danger:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.5);
}

.btn-ghost-light {
  color: #ebedef;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}

.btn-ghost-light:hover {
  color: #4f5d73;
  background-color: #ebedef;
  border-color: #ebedef;
}

.btn-ghost-light:focus, .btn-ghost-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(235, 237, 239, 0.5);
}

.btn-ghost-light.disabled, .btn-ghost-light:disabled {
  color: #ebedef;
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost-light:not(:disabled):not(.disabled):active, .btn-ghost-light:not(:disabled):not(.disabled).active,
.show > .btn-ghost-light.dropdown-toggle {
  color: #4f5d73;
  background-color: #ebedef;
  border-color: #ebedef;
}

.btn-ghost-light:not(:disabled):not(.disabled):active:focus, .btn-ghost-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(235, 237, 239, 0.5);
}

.btn-ghost-dark {
  color: #636f83;
  background-color: transparent;
  background-image: none;
  border-color: transparent;
}

.btn-ghost-dark:hover {
  color: #fff;
  background-color: #636f83;
  border-color: #636f83;
}

.btn-ghost-dark:focus, .btn-ghost-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 111, 131, 0.5);
}

.btn-ghost-dark.disabled, .btn-ghost-dark:disabled {
  color: #636f83;
  background-color: transparent;
  border-color: transparent;
}

.btn-ghost-dark:not(:disabled):not(.disabled):active, .btn-ghost-dark:not(:disabled):not(.disabled).active,
.show > .btn-ghost-dark.dropdown-toggle {
  color: #fff;
  background-color: #636f83;
  border-color: #636f83;
}

.btn-ghost-dark:not(:disabled):not(.disabled):active:focus, .btn-ghost-dark:not(:disabled):not(.disabled).active:focus,
.show > .btn-ghost-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 111, 131, 0.5);
}

.btn-facebook {
  color: #fff;
  background-color: #3b5998;
  border-color: #3b5998;
}

.btn-facebook:hover,.btn-facebook:focus, .btn-facebook.focus {
  color: #fff;
  background-color: #30497c;
  border-color: #2d4373;
}

.btn-facebook:focus, .btn-facebook.focus {
  box-shadow: 0 0 0 0.2rem rgba(88, 114, 167, 0.5);
}

.btn-facebook.disabled, .btn-facebook:disabled {
  color: #fff;
  background-color: #3b5998;
  border-color: #3b5998;
}

.btn-facebook:not(:disabled):not(.disabled):active, .btn-facebook:not(:disabled):not(.disabled).active,
.show > .btn-facebook.dropdown-toggle {
  color: #fff;
  background-color: #2d4373;
  border-color: #293e6a;
}

.btn-facebook:not(:disabled):not(.disabled):active:focus, .btn-facebook:not(:disabled):not(.disabled).active:focus,
.show > .btn-facebook.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(88, 114, 167, 0.5);
}

.btn-twitter {
  color: #fff;
  background-color: #00aced;
  border-color: #00aced;
}

.btn-twitter:hover,.btn-twitter:focus, .btn-twitter.focus {
  color: #fff;
  background-color: #0090c7;
  border-color: #0087ba;
}

.btn-twitter:focus, .btn-twitter.focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 184, 240, 0.5);
}

.btn-twitter.disabled, .btn-twitter:disabled {
  color: #fff;
  background-color: #00aced;
  border-color: #00aced;
}

.btn-twitter:not(:disabled):not(.disabled):active, .btn-twitter:not(:disabled):not(.disabled).active,
.show > .btn-twitter.dropdown-toggle {
  color: #fff;
  background-color: #0087ba;
  border-color: #007ead;
}

.btn-twitter:not(:disabled):not(.disabled):active:focus, .btn-twitter:not(:disabled):not(.disabled).active:focus,
.show > .btn-twitter.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 184, 240, 0.5);
}

.btn-linkedin {
  color: #fff;
  background-color: #4875b4;
  border-color: #4875b4;
}

.btn-linkedin:hover,.btn-linkedin:focus, .btn-linkedin.focus {
  color: #fff;
  background-color: #3d6399;
  border-color: #395d90;
}

.btn-linkedin:focus, .btn-linkedin.focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 138, 191, 0.5);
}

.btn-linkedin.disabled, .btn-linkedin:disabled {
  color: #fff;
  background-color: #4875b4;
  border-color: #4875b4;
}

.btn-linkedin:not(:disabled):not(.disabled):active, .btn-linkedin:not(:disabled):not(.disabled).active,
.show > .btn-linkedin.dropdown-toggle {
  color: #fff;
  background-color: #395d90;
  border-color: #365786;
}

.btn-linkedin:not(:disabled):not(.disabled):active:focus, .btn-linkedin:not(:disabled):not(.disabled).active:focus,
.show > .btn-linkedin.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(99, 138, 191, 0.5);
}

.btn-flickr {
  color: #fff;
  background-color: #ff0084;
  border-color: #ff0084;
}

.btn-flickr:hover,.btn-flickr:focus, .btn-flickr.focus {
  color: #fff;
  background-color: #d90070;
  border-color: #cc006a;
}

.btn-flickr:focus, .btn-flickr.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 38, 150, 0.5);
}

.btn-flickr.disabled, .btn-flickr:disabled {
  color: #fff;
  background-color: #ff0084;
  border-color: #ff0084;
}

.btn-flickr:not(:disabled):not(.disabled):active, .btn-flickr:not(:disabled):not(.disabled).active,
.show > .btn-flickr.dropdown-toggle {
  color: #fff;
  background-color: #cc006a;
  border-color: #bf0063;
}

.btn-flickr:not(:disabled):not(.disabled):active:focus, .btn-flickr:not(:disabled):not(.disabled).active:focus,
.show > .btn-flickr.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 38, 150, 0.5);
}

.btn-tumblr {
  color: #fff;
  background-color: #32506d;
  border-color: #32506d;
}

.btn-tumblr:hover,.btn-tumblr:focus, .btn-tumblr.focus {
  color: #fff;
  background-color: #263d53;
  border-color: #22364a;
}

.btn-tumblr:focus, .btn-tumblr.focus {
  box-shadow: 0 0 0 0.2rem rgba(81, 106, 131, 0.5);
}

.btn-tumblr.disabled, .btn-tumblr:disabled {
  color: #fff;
  background-color: #32506d;
  border-color: #32506d;
}

.btn-tumblr:not(:disabled):not(.disabled):active, .btn-tumblr:not(:disabled):not(.disabled).active,
.show > .btn-tumblr.dropdown-toggle {
  color: #fff;
  background-color: #22364a;
  border-color: #1e3041;
}

.btn-tumblr:not(:disabled):not(.disabled):active:focus, .btn-tumblr:not(:disabled):not(.disabled).active:focus,
.show > .btn-tumblr.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(81, 106, 131, 0.5);
}

.btn-xing {
  color: #fff;
  background-color: #026466;
  border-color: #026466;
}

.btn-xing:hover,.btn-xing:focus, .btn-xing.focus {
  color: #fff;
  background-color: #013f40;
  border-color: #013334;
}

.btn-xing:focus, .btn-xing.focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 123, 125, 0.5);
}

.btn-xing.disabled, .btn-xing:disabled {
  color: #fff;
  background-color: #026466;
  border-color: #026466;
}

.btn-xing:not(:disabled):not(.disabled):active, .btn-xing:not(:disabled):not(.disabled).active,
.show > .btn-xing.dropdown-toggle {
  color: #fff;
  background-color: #013334;
  border-color: #012727;
}

.btn-xing:not(:disabled):not(.disabled):active:focus, .btn-xing:not(:disabled):not(.disabled).active:focus,
.show > .btn-xing.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 123, 125, 0.5);
}

.btn-github {
  color: #fff;
  background-color: #4183c4;
  border-color: #4183c4;
}

.btn-github:hover,.btn-github:focus, .btn-github.focus {
  color: #fff;
  background-color: #3570aa;
  border-color: #3269a0;
}

.btn-github:focus, .btn-github.focus {
  box-shadow: 0 0 0 0.2rem rgba(94, 150, 205, 0.5);
}

.btn-github.disabled, .btn-github:disabled {
  color: #fff;
  background-color: #4183c4;
  border-color: #4183c4;
}

.btn-github:not(:disabled):not(.disabled):active, .btn-github:not(:disabled):not(.disabled).active,
.show > .btn-github.dropdown-toggle {
  color: #fff;
  background-color: #3269a0;
  border-color: #2f6397;
}

.btn-github:not(:disabled):not(.disabled):active:focus, .btn-github:not(:disabled):not(.disabled).active:focus,
.show > .btn-github.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(94, 150, 205, 0.5);
}

.btn-stack-overflow {
  color: #fff;
  background-color: #fe7a15;
  border-color: #fe7a15;
}

.btn-stack-overflow:hover,.btn-stack-overflow:focus, .btn-stack-overflow.focus {
  color: #fff;
  background-color: #ec6701;
  border-color: #df6101;
}

.btn-stack-overflow:focus, .btn-stack-overflow.focus {
  box-shadow: 0 0 0 0.2rem rgba(254, 142, 56, 0.5);
}

.btn-stack-overflow.disabled, .btn-stack-overflow:disabled {
  color: #fff;
  background-color: #fe7a15;
  border-color: #fe7a15;
}

.btn-stack-overflow:not(:disabled):not(.disabled):active, .btn-stack-overflow:not(:disabled):not(.disabled).active,
.show > .btn-stack-overflow.dropdown-toggle {
  color: #fff;
  background-color: #df6101;
  border-color: #d25c01;
}

.btn-stack-overflow:not(:disabled):not(.disabled):active:focus, .btn-stack-overflow:not(:disabled):not(.disabled).active:focus,
.show > .btn-stack-overflow.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(254, 142, 56, 0.5);
}

.btn-youtube {
  color: #fff;
  background-color: #b00;
  border-color: #b00;
}

.btn-youtube:hover,.btn-youtube:focus, .btn-youtube.focus {
  color: #fff;
  background-color: #950000;
  border-color: #880000;
}

.btn-youtube:focus, .btn-youtube.focus {
  box-shadow: 0 0 0 0.2rem rgba(197, 38, 38, 0.5);
}

.btn-youtube.disabled, .btn-youtube:disabled {
  color: #fff;
  background-color: #b00;
  border-color: #b00;
}

.btn-youtube:not(:disabled):not(.disabled):active, .btn-youtube:not(:disabled):not(.disabled).active,
.show > .btn-youtube.dropdown-toggle {
  color: #fff;
  background-color: #880000;
  border-color: #7b0000;
}

.btn-youtube:not(:disabled):not(.disabled):active:focus, .btn-youtube:not(:disabled):not(.disabled).active:focus,
.show > .btn-youtube.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(197, 38, 38, 0.5);
}

.btn-dribbble {
  color: #fff;
  background-color: #ea4c89;
  border-color: #ea4c89;
}

.btn-dribbble:hover,.btn-dribbble:focus, .btn-dribbble.focus {
  color: #fff;
  background-color: #e62a72;
  border-color: #e51e6b;
}

.btn-dribbble:focus, .btn-dribbble.focus {
  box-shadow: 0 0 0 0.2rem rgba(237, 103, 155, 0.5);
}

.btn-dribbble.disabled, .btn-dribbble:disabled {
  color: #fff;
  background-color: #ea4c89;
  border-color: #ea4c89;
}

.btn-dribbble:not(:disabled):not(.disabled):active, .btn-dribbble:not(:disabled):not(.disabled).active,
.show > .btn-dribbble.dropdown-toggle {
  color: #fff;
  background-color: #e51e6b;
  border-color: #dc1a65;
}

.btn-dribbble:not(:disabled):not(.disabled):active:focus, .btn-dribbble:not(:disabled):not(.disabled).active:focus,
.show > .btn-dribbble.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(237, 103, 155, 0.5);
}

.btn-instagram {
  color: #fff;
  background-color: #517fa4;
  border-color: #517fa4;
}

.btn-instagram:hover,.btn-instagram:focus, .btn-instagram.focus {
  color: #fff;
  background-color: #446b8a;
  border-color: #406582;
}

.btn-instagram:focus, .btn-instagram.focus {
  box-shadow: 0 0 0 0.2rem rgba(107, 146, 178, 0.5);
}

.btn-instagram.disabled, .btn-instagram:disabled {
  color: #fff;
  background-color: #517fa4;
  border-color: #517fa4;
}

.btn-instagram:not(:disabled):not(.disabled):active, .btn-instagram:not(:disabled):not(.disabled).active,
.show > .btn-instagram.dropdown-toggle {
  color: #fff;
  background-color: #406582;
  border-color: #3c5e79;
}

.btn-instagram:not(:disabled):not(.disabled):active:focus, .btn-instagram:not(:disabled):not(.disabled).active:focus,
.show > .btn-instagram.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(107, 146, 178, 0.5);
}

.btn-pinterest {
  color: #fff;
  background-color: #cb2027;
  border-color: #cb2027;
}

.btn-pinterest:hover,.btn-pinterest:focus, .btn-pinterest.focus {
  color: #fff;
  background-color: #aa1b21;
  border-color: #9f191f;
}

.btn-pinterest:focus, .btn-pinterest.focus {
  box-shadow: 0 0 0 0.2rem rgba(211, 65, 71, 0.5);
}

.btn-pinterest.disabled, .btn-pinterest:disabled {
  color: #fff;
  background-color: #cb2027;
  border-color: #cb2027;
}

.btn-pinterest:not(:disabled):not(.disabled):active, .btn-pinterest:not(:disabled):not(.disabled).active,
.show > .btn-pinterest.dropdown-toggle {
  color: #fff;
  background-color: #9f191f;
  border-color: #94171c;
}

.btn-pinterest:not(:disabled):not(.disabled):active:focus, .btn-pinterest:not(:disabled):not(.disabled).active:focus,
.show > .btn-pinterest.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(211, 65, 71, 0.5);
}

.btn-vk {
  color: #fff;
  background-color: #45668e;
  border-color: #45668e;
}

.btn-vk:hover,.btn-vk:focus, .btn-vk.focus {
  color: #fff;
  background-color: #385474;
  border-color: #344d6c;
}

.btn-vk:focus, .btn-vk.focus {
  box-shadow: 0 0 0 0.2rem rgba(97, 125, 159, 0.5);
}

.btn-vk.disabled, .btn-vk:disabled {
  color: #fff;
  background-color: #45668e;
  border-color: #45668e;
}

.btn-vk:not(:disabled):not(.disabled):active, .btn-vk:not(:disabled):not(.disabled).active,
.show > .btn-vk.dropdown-toggle {
  color: #fff;
  background-color: #344d6c;
  border-color: #304763;
}

.btn-vk:not(:disabled):not(.disabled):active:focus, .btn-vk:not(:disabled):not(.disabled).active:focus,
.show > .btn-vk.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(97, 125, 159, 0.5);
}

.btn-yahoo {
  color: #fff;
  background-color: #400191;
  border-color: #400191;
}

.btn-yahoo:hover,.btn-yahoo:focus, .btn-yahoo.focus {
  color: #fff;
  background-color: #2f016b;
  border-color: #2a015e;
}

.btn-yahoo:focus, .btn-yahoo.focus {
  box-shadow: 0 0 0 0.2rem rgba(93, 39, 162, 0.5);
}

.btn-yahoo.disabled, .btn-yahoo:disabled {
  color: #fff;
  background-color: #400191;
  border-color: #400191;
}

.btn-yahoo:not(:disabled):not(.disabled):active, .btn-yahoo:not(:disabled):not(.disabled).active,
.show > .btn-yahoo.dropdown-toggle {
  color: #fff;
  background-color: #2a015e;
  border-color: #240152;
}

.btn-yahoo:not(:disabled):not(.disabled):active:focus, .btn-yahoo:not(:disabled):not(.disabled).active:focus,
.show > .btn-yahoo.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(93, 39, 162, 0.5);
}

.btn-behance {
  color: #fff;
  background-color: #1769ff;
  border-color: #1769ff;
}

.btn-behance:hover,.btn-behance:focus, .btn-behance.focus {
  color: #fff;
  background-color: #0055f0;
  border-color: #0050e3;
}

.btn-behance:focus, .btn-behance.focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 128, 255, 0.5);
}

.btn-behance.disabled, .btn-behance:disabled {
  color: #fff;
  background-color: #1769ff;
  border-color: #1769ff;
}

.btn-behance:not(:disabled):not(.disabled):active, .btn-behance:not(:disabled):not(.disabled).active,
.show > .btn-behance.dropdown-toggle {
  color: #fff;
  background-color: #0050e3;
  border-color: #004cd6;
}

.btn-behance:not(:disabled):not(.disabled):active:focus, .btn-behance:not(:disabled):not(.disabled).active:focus,
.show > .btn-behance.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 128, 255, 0.5);
}

.btn-reddit {
  color: #fff;
  background-color: #ff4500;
  border-color: #ff4500;
}

.btn-reddit:hover,.btn-reddit:focus, .btn-reddit.focus {
  color: #fff;
  background-color: #d93b00;
  border-color: #cc3700;
}

.btn-reddit:focus, .btn-reddit.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 97, 38, 0.5);
}

.btn-reddit.disabled, .btn-reddit:disabled {
  color: #fff;
  background-color: #ff4500;
  border-color: #ff4500;
}

.btn-reddit:not(:disabled):not(.disabled):active, .btn-reddit:not(:disabled):not(.disabled).active,
.show > .btn-reddit.dropdown-toggle {
  color: #fff;
  background-color: #cc3700;
  border-color: #bf3400;
}

.btn-reddit:not(:disabled):not(.disabled):active:focus, .btn-reddit:not(:disabled):not(.disabled).active:focus,
.show > .btn-reddit.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 97, 38, 0.5);
}

.btn-vimeo {
  color: #4f5d73;
  background-color: #aad450;
  border-color: #aad450;
}

.btn-vimeo:hover,.btn-vimeo:focus, .btn-vimeo.focus {
  color: #4f5d73;
  background-color: #9bcc32;
  border-color: #93c130;
}

.btn-vimeo:focus, .btn-vimeo.focus {
  box-shadow: 0 0 0 0.2rem rgba(156, 194, 85, 0.5);
}

.btn-vimeo.disabled, .btn-vimeo:disabled {
  color: #4f5d73;
  background-color: #aad450;
  border-color: #aad450;
}

.btn-vimeo:not(:disabled):not(.disabled):active, .btn-vimeo:not(:disabled):not(.disabled).active,
.show > .btn-vimeo.dropdown-toggle {
  color: #4f5d73;
  background-color: #93c130;
  border-color: #8bb72d;
}

.btn-vimeo:not(:disabled):not(.disabled):active:focus, .btn-vimeo:not(:disabled):not(.disabled).active:focus,
.show > .btn-vimeo.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(156, 194, 85, 0.5);
}

.c-callout {
  position: relative;
  padding: 0 1rem;
  margin: 1rem 0;
  border-radius: 0.25rem;
}

html:not([dir="rtl"]) .c-callout {
  border-left: 4px solid #d8dbe0;
}

*[dir="rtl"] .c-callout {
  border-right: 4px solid #d8dbe0;
}

.c-callout-bordered {
  border: 1px solid #d8dbe0;
  border-left-width: 4px;
}

.c-callout code {
  border-radius: 0.25rem;
}

.c-callout h4 {
  margin-top: 0;
  margin-bottom: .25rem;
}

.c-callout p:last-child {
  margin-bottom: 0;
}

.c-callout + .c-callout {
  margin-top: -0.25rem;
}

html:not([dir="rtl"]) .c-callout-primary {
  border-left-color: #321fdb;
}

*[dir="rtl"] .c-callout-primary {
  border-right-color: #321fdb;
}

.c-callout-primary h4 {
  color: #321fdb;
}

html:not([dir="rtl"]) .c-callout-secondary {
  border-left-color: #ced2d8;
}

*[dir="rtl"] .c-callout-secondary {
  border-right-color: #ced2d8;
}

.c-callout-secondary h4 {
  color: #ced2d8;
}

html:not([dir="rtl"]) .c-callout-success {
  border-left-color: #2eb85c;
}

*[dir="rtl"] .c-callout-success {
  border-right-color: #2eb85c;
}

.c-callout-success h4 {
  color: #2eb85c;
}

html:not([dir="rtl"]) .c-callout-info {
  border-left-color: #39f;
}

*[dir="rtl"] .c-callout-info {
  border-right-color: #39f;
}

.c-callout-info h4 {
  color: #39f;
}

html:not([dir="rtl"]) .c-callout-warning {
  border-left-color: #f9b115;
}

*[dir="rtl"] .c-callout-warning {
  border-right-color: #f9b115;
}

.c-callout-warning h4 {
  color: #f9b115;
}

html:not([dir="rtl"]) .c-callout-danger {
  border-left-color: #e55353;
}

*[dir="rtl"] .c-callout-danger {
  border-right-color: #e55353;
}

.c-callout-danger h4 {
  color: #e55353;
}

html:not([dir="rtl"]) .c-callout-light {
  border-left-color: #ebedef;
}

*[dir="rtl"] .c-callout-light {
  border-right-color: #ebedef;
}

.c-callout-light h4 {
  color: #ebedef;
}

html:not([dir="rtl"]) .c-callout-dark {
  border-left-color: #636f83;
}

*[dir="rtl"] .c-callout-dark {
  border-right-color: #636f83;
}

.c-callout-dark h4 {
  color: #636f83;
}

.card {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  margin-bottom: 1.5rem;
  word-wrap: break-word;
  background-clip: border-box;
  border: 1px solid;
  border-radius: 0.25rem;
  background-color: #fff;
  border-color: #d8dbe0;
}

.card > hr {
  margin-right: 0;
  margin-left: 0;
}

.card > .list-group:first-child .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.card > .list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.card.drag,
.card .drag {
  cursor: move;
}

.card.bg-primary {
  border-color: #2517a3;
}

.card.bg-primary .card-header {
  background-color: #2f1dce;
  border-color: #2517a3;
}

.card.bg-secondary {
  border-color: #abb1bc;
}

.card.bg-secondary .card-header {
  background-color: #c5cad1;
  border-color: #abb1bc;
}

.card.bg-success {
  border-color: #218543;
}

.card.bg-success .card-header {
  background-color: #2bac56;
  border-color: #218543;
}

.card.bg-info {
  border-color: #0079f2;
}

.card.bg-info .card-header {
  background-color: #2491ff;
  border-color: #0079f2;
}

.card.bg-warning {
  border-color: #c98b05;
}

.card.bg-warning .card-header {
  background-color: #f8ac06;
  border-color: #c98b05;
}

.card.bg-danger {
  border-color: #d82121;
}

.card.bg-danger .card-header {
  background-color: #e34646;
  border-color: #d82121;
}

.card.bg-light {
  border-color: #c8cdd3;
}

.card.bg-light .card-header {
  background-color: #e3e5e8;
  border-color: #c8cdd3;
}

.card.bg-dark {
  border-color: #48505f;
}

.card.bg-dark .card-header {
  background-color: #5c687a;
  border-color: #48505f;
}

.card-body {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  min-height: 1px;
  padding: 1.25rem;
}

.card-title {
  margin-bottom: 0.75rem;
}

.card-subtitle {
  margin-top: -0.375rem;
}

.card-subtitle,.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}

html:not([dir="rtl"]) .card-link + .card-link {
  margin-left: 1.25rem;
}

*[dir="rtl"] .card-link + .card-link {
  margin-right: 1.25rem;
}

.card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  border-bottom: 1px solid;
  background-color: #fff;
  border-color: #d8dbe0;
}

.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.card-header + .list-group .list-group-item:first-child {
  border-top: 0;
}

.card-header .c-chart-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
}

.card-footer {
  padding: 0.75rem 1.25rem;
  border-top: 1px solid;
  background-color: #fff;
  border-color: #d8dbe0;
}

.card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

.card-header-tabs {
  margin-bottom: -0.75rem;
  border-bottom: 0;
}

.card-header-tabs,.card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
}

.card-img,
.card-img-top,
.card-img-bottom {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

.card-deck .card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  .card-deck {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }
  .card-deck .card {
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}

.card-group > .card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  .card-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
  }
  .card-group > .card {
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  html:not([dir="rtl"]) .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  *[dir="rtl"] .card-group > .card + .card {
    margin-right: 0;
    border-right: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
  .card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
  .card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
  .card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
  .card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.card-columns .card {
  margin-bottom: 0.75rem;
}

@media (min-width: 576px) {
  .card-columns {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 1.25rem;
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }
  .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}

.accordion > .card {
  overflow: hidden;
}

.accordion > .card:not(:last-of-type) {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.accordion > .card:not(:first-of-type) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.accordion > .card > .card-header {
  border-radius: 0;
  margin-bottom: -1px;
}

.card-placeholder {
  background: rgba(0, 0, 21, 0.025);
  border: 1px dashed #c4c9d0;
}

.card-header-icon-bg {
  width: 2.8125rem;
  padding: 0.75rem 0;
  margin: -0.75rem 1.25rem -0.75rem -1.25rem;
  line-height: inherit;
  color: #4f5d73;
  text-align: center;
  background: transparent;
  border-right: 1px solid;
  border-right: #d8dbe0;
}

.card-header-icon-bg,.card-header-actions {
  display: inline-block;
}

html:not([dir="rtl"]) .card-header-actions {
  float: right;
  margin-right: -0.25rem;
}

*[dir="rtl"] .card-header-actions {
  float: left;
  margin-left: -0.25rem;
}

.card-header-action {
  padding: 0 0.25rem;
  color: #8a93a2;
}

.card-header-action:hover {
  color: #4f5d73;
  text-decoration: none;
}

.card-accent-primary {
  border-top: 2px solid #321fdb !important;
}

.card-accent-secondary {
  border-top: 2px solid #ced2d8 !important;
}

.card-accent-success {
  border-top: 2px solid #2eb85c !important;
}

.card-accent-info {
  border-top: 2px solid #39f !important;
}

.card-accent-warning {
  border-top: 2px solid #f9b115 !important;
}

.card-accent-danger {
  border-top: 2px solid #e55353 !important;
}

.card-accent-light {
  border-top: 2px solid #ebedef !important;
}

.card-accent-dark {
  border-top: 2px solid #636f83 !important;
}

.card-full {
  margin-top: -1rem;
  margin-right: -15px;
  margin-left: -15px;
  border: 0;
  border-bottom: 1px solid #d8dbe0;
}

@media (min-width: 576px) {
  .card-columns.cols-2 {
    -webkit-column-count: 2;
    -moz-column-count: 2;
    column-count: 2;
  }
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transition: -webkit-transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-left),
.active.carousel-item-right {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-right),
.active.carousel-item-left {
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}

.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  -webkit-transform: none;
  transform: none;
}

.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-left,
.carousel-fade .carousel-item-prev.carousel-item-right {
  z-index: 1;
  opacity: 1;
}

.carousel-fade .active.carousel-item-left,
.carousel-fade .active.carousel-item-right {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}

@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-left,
  .carousel-fade .active.carousel-item-right {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 15%;
  color: #fff;
  text-align: center;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}

@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
  .carousel-control-next {
    transition: none;
  }
}

.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: no-repeat 50% / 100% 100%;
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5L4.25 4l2.5-2.5L5.25 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5L3.75 4l-2.5 2.5L2.75 8l4-4-4-4z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}

html:not([dir="rtl"]) .carousel-indicators {
  padding-left: 0;
}

*[dir="rtl"] .carousel-indicators {
  padding-right: 0;
}

.carousel-indicators li {
  box-sizing: content-box;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: .5;
  transition: opacity 0.6s ease;
}

@media (prefers-reduced-motion: reduce) {
  .carousel-indicators li {
    transition: none;
  }
}

.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}

.c-chart-wrapper canvas {
  width: 100%;
}

base-chart.chart {
  display: block;
}

canvas {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.close {
  float: right;
  font-size: 1.3125rem;
  font-weight: 700;
  line-height: 1;
  opacity: .5;
  color: #000015;
  text-shadow: 0 1px 0 #fff;
}

.close:hover {
  text-decoration: none;
  color: #000015;
}

.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus {
  opacity: .75;
}

button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

a.close.disabled {
  pointer-events: none;
}

code {
  font-size: 87.5%;
  color: #e83e8c;
  word-wrap: break-word;
}

a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #4f5d73;
  border-radius: 0.2rem;
}

kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}

pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

.custom-control {
  position: relative;
  display: block;
  min-height: 1.3125rem;
}

html:not([dir="rtl"]) .custom-control {
  padding-left: 1.5rem;
}

*[dir="rtl"] .custom-control {
  padding-right: 1.5rem;
}

.custom-control-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 1rem;
}

.custom-control-input {
  position: absolute;
  z-index: -1;
  width: 1rem;
  height: 1.15625rem;
  opacity: 0;
}

html:not([dir="rtl"]) .custom-control-input {
  left: 0;
}

*[dir="rtl"] .custom-control-input {
  right: 0;
}

.custom-control-input:checked ~ .custom-control-label::before {
  color: #fff;
  border-color: #321fdb;
  background-color: #321fdb;
}

.custom-control-input:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-color: #958bef;
}

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  color: #fff;
  background-color: #beb8f5;
  border-color: #beb8f5;
}

.custom-control-input[disabled] ~ .custom-control-label, .custom-control-input:disabled ~ .custom-control-label {
  color: #8a93a2;
}

.custom-control-input[disabled] ~ .custom-control-label::before, .custom-control-input:disabled ~ .custom-control-label::before {
  background-color: #d8dbe0;
}

.custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}

.custom-control-label::before {
  position: absolute;
  top: 0.15625rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  border: solid 1px;
  background-color: #fff;
  border-color: #9da5b1;
}

html:not([dir="rtl"]) .custom-control-label::before {
  left: -1.5rem;
}

*[dir="rtl"] .custom-control-label::before {
  right: -1.5rem;
}

.custom-control-label::after {
  position: absolute;
  top: 0.15625rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background: no-repeat 50% / 50% 50%;
}

html:not([dir="rtl"]) .custom-control-label::after {
  left: -1.5rem;
}

*[dir="rtl"] .custom-control-label::after {
  right: -1.5rem;
}

.custom-checkbox .custom-control-label::before {
  border-radius: 0.25rem;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
}

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
  border-color: #321fdb;
  background-color: #321fdb;
}

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(50, 31, 219, 0.5);
}

.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
  background-color: rgba(50, 31, 219, 0.5);
}

.custom-radio .custom-control-label::before {
  border-radius: 50%;
}

.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(50, 31, 219, 0.5);
}

html:not([dir="rtl"]) .custom-switch {
  padding-left: 2.25rem;
}

*[dir="rtl"] .custom-switch {
  padding-right: 2.25rem;
}

.custom-switch .custom-control-label::before {
  width: 1.75rem;
  pointer-events: all;
  border-radius: 0.5rem;
}

html:not([dir="rtl"]) .custom-switch .custom-control-label::before {
  left: -2.25rem;
}

*[dir="rtl"] .custom-switch .custom-control-label::before {
  right: -2.25rem;
}

.custom-switch .custom-control-label::after {
  top: calc(0.15625rem + 2px);
  width: calc(1rem - 4px);
  height: calc(1rem - 4px);
  background-color: #9da5b1;
  border-radius: 0.5rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;
  background-color: #9da5b1;
}

html:not([dir="rtl"]) .custom-switch .custom-control-label::after {
  left: calc(-2.25rem + 2px);
}

*[dir="rtl"] .custom-switch .custom-control-label::after {
  right: calc(-2.25rem + 2px);
}

@media (prefers-reduced-motion: reduce) {
  .custom-switch .custom-control-label::after {
    transition: none;
  }
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
  background-color: #fff;
  -webkit-transform: translateX(0.75rem);
  transform: translateX(0.75rem);
}

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: rgba(50, 31, 219, 0.5);
}

.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  vertical-align: middle;
  border: 1px solid;
  border-radius: 0.25rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  color: #768192;
  background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23636f83' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
  border-color: #d8dbe0;
}

.custom-select:focus {
  border-color: #958bef;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

.custom-select:focus::-ms-value {
  color: #768192;
  background-color: #fff;
}

.custom-select[multiple], .custom-select[size]:not([size="1"]) {
  height: auto;
  background-image: none;
}

html:not([dir="rtl"]) .custom-select[multiple], html:not([dir="rtl"]) .custom-select[size]:not([size="1"]) {
  padding-right: 0.75rem;
}

*[dir="rtl"] .custom-select[multiple], *[dir="rtl"] .custom-select[size]:not([size="1"]) {
  padding-left: 0.75rem;
}

.custom-select:disabled {
  color: #8a93a2;
  background-color: #d8dbe0;
}

.custom-select::-ms-expand {
  display: none;
}

.custom-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #768192;
}

.custom-select-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.765625rem;
}

html:not([dir="rtl"]) .custom-select-sm {
  padding-left: 0.5rem;
}

*[dir="rtl"] .custom-select-sm {
  padding-right: 0.5rem;
}

.custom-select-lg {
  height: calc(1.5em + 1rem + 2px);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1.09375rem;
}

html:not([dir="rtl"]) .custom-select-lg {
  padding-left: 1rem;
}

*[dir="rtl"] .custom-select-lg {
  padding-right: 1rem;
}

.custom-file {
  display: inline-block;
  margin-bottom: 0;
}

.custom-file,.custom-file-input {
  position: relative;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
}

.custom-file-input {
  z-index: 2;
  margin: 0;
  opacity: 0;
}

.custom-file-input:focus ~ .custom-file-label {
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
  border-color: #958bef;
}

.custom-file-input[disabled] ~ .custom-file-label,
.custom-file-input:disabled ~ .custom-file-label {
  background-color: #d8dbe0;
}

.custom-file-input:lang(en) ~ .custom-file-label::after {
  content: "Browse";
}

.custom-file-input ~ .custom-file-label[data-browse]::after {
  content: attr(data-browse);
}

.custom-file-label {
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(1.5em + 0.75rem + 2px);
  font-weight: 400;
  border: 1px solid;
  border-radius: 0.25rem;
  background-color: #fff;
  border-color: #d8dbe0;
}

.custom-file-label,.custom-file-label::after {
  position: absolute;
  top: 0;
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #768192;
}

.custom-file-label::after {
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(1.5em + 0.75rem);
  content: "Browse";
  border-left: inherit;
  border-radius: 0 0.25rem 0.25rem 0;
  background-color: #ebedef;
}

html:not([dir="rtl"]) .custom-file-label::after {
  right: 0;
}

*[dir="rtl"] .custom-file-label::after {
  left: 0;
}

.custom-range {
  width: 100%;
  height: 1.4rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.custom-range:focus {
  outline: none;
}

.custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #ebedef, 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

.custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #ebedef, 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

.custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #ebedef, 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

.custom-range::-moz-focus-outer {
  border: 0;
}

.custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #321fdb;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .custom-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}

.custom-range::-webkit-slider-thumb:active {
  background-color: ghten(#321fdb, 35%);
}

.custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  border-color: transparent;
  border-radius: 1rem;
  background-color: #c4c9d0;
}

.custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #321fdb;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .custom-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}

.custom-range::-moz-range-thumb:active {
  background-color: ghten(#321fdb, 35%);
}

.custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #c4c9d0;
  border-color: transparent;
  border-radius: 1rem;
}

.custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
  background-color: #321fdb;
  border: 0;
  border-radius: 1rem;
  -ms-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .custom-range::-ms-thumb {
    -ms-transition: none;
    transition: none;
  }
}

.custom-range::-ms-thumb:active {
  background-color: ghten(#321fdb, 35%);
}

.custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: 0.5rem;
}

.custom-range::-ms-fill-lower,.custom-range::-ms-fill-upper {
  background-color: #c4c9d0;
  border-radius: 1rem;
}

.custom-range::-ms-fill-upper {
  margin-right: 15px;
}

.custom-range:disabled::-webkit-slider-thumb {
  background-color: #9da5b1;
}

.custom-range:disabled::-webkit-slider-runnable-track {
  cursor: default;
}

.custom-range:disabled::-moz-range-thumb {
  background-color: #9da5b1;
}

.custom-range:disabled::-moz-range-track {
  cursor: default;
}

.custom-range:disabled::-ms-thumb {
  background-color: #9da5b1;
}

.custom-control-label::before,
.custom-file-label,
.custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .custom-control-label::before,
  .custom-file-label,
  .custom-select {
    transition: none;
  }
}

.dropup,
.dropright,
.dropdown,
.dropleft {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}

.dropdown-toggle::after {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

html:not([dir="rtl"]) .dropdown-toggle::after {
  margin-left: 0.255em;
}

*[dir="rtl"] .dropdown-toggle::after {
  margin-right: 0.255em;
}

html:not([dir="rtl"]) .dropdown-toggle:empty::after {
  margin-left: 0;
}

*[dir="rtl"] .dropdown-toggle:empty::after {
  margin-right: 0;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 0.875rem;
  text-align: left;
  list-style: none;
  background-clip: padding-box;
  border: 1px solid;
  border-radius: 0.25rem;
  color: #4f5d73;
  background-color: #fff;
  border-color: #d8dbe0;
}

html:not([dir="rtl"]) .dropdown-menu {
  left: 0;
}

*[dir="rtl"] .dropdown-menu {
  right: 0;
}

html:not([dir="rtl"]) .dropdown-menu-left {
  right: auto;
  left: 0;
}

*[dir="rtl"] .dropdown-menu-left,html:not([dir="rtl"]) .dropdown-menu-right {
  right: 0;
  left: auto;
}

*[dir="rtl"] .dropdown-menu-right {
  right: auto;
  left: 0;
}

@media (min-width: 576px) {
  html:not([dir="rtl"]) .dropdown-menu-sm-left {
    right: auto;
    left: 0;
  }
  *[dir="rtl"] .dropdown-menu-sm-left,html:not([dir="rtl"]) .dropdown-menu-sm-right {
    right: 0;
    left: auto;
  }
  *[dir="rtl"] .dropdown-menu-sm-right {
    right: auto;
    left: 0;
  }
}

@media (min-width: 768px) {
  html:not([dir="rtl"]) .dropdown-menu-md-left {
    right: auto;
    left: 0;
  }
  *[dir="rtl"] .dropdown-menu-md-left,html:not([dir="rtl"]) .dropdown-menu-md-right {
    right: 0;
    left: auto;
  }
  *[dir="rtl"] .dropdown-menu-md-right {
    right: auto;
    left: 0;
  }
}

@media (min-width: 992px) {
  html:not([dir="rtl"]) .dropdown-menu-lg-left {
    right: auto;
    left: 0;
  }
  *[dir="rtl"] .dropdown-menu-lg-left,html:not([dir="rtl"]) .dropdown-menu-lg-right {
    right: 0;
    left: auto;
  }
  *[dir="rtl"] .dropdown-menu-lg-right {
    right: auto;
    left: 0;
  }
}

@media (min-width: 1200px) {
  html:not([dir="rtl"]) .dropdown-menu-xl-left {
    right: auto;
    left: 0;
  }
  *[dir="rtl"] .dropdown-menu-xl-left,html:not([dir="rtl"]) .dropdown-menu-xl-right {
    right: 0;
    left: auto;
  }
  *[dir="rtl"] .dropdown-menu-xl-right {
    right: auto;
    left: 0;
  }
}

.dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}

.dropup .dropdown-toggle::after {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}

html:not([dir="rtl"]) .dropup .dropdown-toggle::after {
  margin-left: 0.255em;
}

*[dir="rtl"] .dropup .dropdown-toggle::after {
  margin-right: 0.255em;
}

html:not([dir="rtl"]) .dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

*[dir="rtl"] .dropup .dropdown-toggle:empty::after {
  margin-right: 0;
}

.dropright .dropdown-menu {
  top: 0;
  margin-top: 0;
}

html:not([dir="rtl"]) .dropright .dropdown-menu {
  right: auto;
  left: 100%;
  margin-left: 0.125rem;
}

*[dir="rtl"] .dropright .dropdown-menu {
  right: 100%;
  left: auto;
  margin-right: 0.125rem;
}

.dropright .dropdown-toggle::after {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
  vertical-align: 0;
}

html:not([dir="rtl"]) .dropright .dropdown-toggle::after {
  margin-left: 0.255em;
}

*[dir="rtl"] .dropright .dropdown-toggle::after {
  margin-right: 0.255em;
}

html:not([dir="rtl"]) .dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}

*[dir="rtl"] .dropright .dropdown-toggle:empty::after {
  margin-right: 0;
}

.dropleft .dropdown-menu {
  top: 0;
  margin-top: 0;
}

html:not([dir="rtl"]) .dropleft .dropdown-menu {
  right: 100%;
  left: auto;
  margin-right: 0.125rem;
}

*[dir="rtl"] .dropleft .dropdown-menu {
  right: auto;
  left: 100%;
  margin-left: 0.125rem;
}

.dropleft .dropdown-toggle::after {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
  display: none;
}

html:not([dir="rtl"]) .dropleft .dropdown-toggle::after {
  margin-left: 0.255em;
}

*[dir="rtl"] .dropleft .dropdown-toggle::after {
  margin-right: 0.255em;
}

.dropleft .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
  vertical-align: 0;
}

html:not([dir="rtl"]) .dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}

*[dir="rtl"] .dropleft .dropdown-toggle:empty::after {
  margin-right: 0;
}

.dropdown-menu[x-placement^="top"], .dropdown-menu[x-placement^="right"], .dropdown-menu[x-placement^="bottom"], .dropdown-menu[x-placement^="left"] {
  right: auto;
  bottom: auto;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #d8dbe0;
}

.dropdown-item {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  padding: 0.5rem 1.25rem;
  clear: both;
  font-weight: 400;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  color: #4f5d73;
}

.dropdown-item:hover, .dropdown-item:focus {
  text-decoration: none;
  color: #455164;
  background-color: #ebedef;
}

.dropdown-item.active, .dropdown-item:active {
  text-decoration: none;
  color: #fff;
  background-color: #321fdb;
}

.dropdown-item.disabled, .dropdown-item:disabled {
  pointer-events: none;
  background-color: transparent;
  color: #8a93a2;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  margin-bottom: 0;
  font-size: 0.765625rem;
  white-space: nowrap;
  color: #8a93a2;
}

.dropdown-header,.dropdown-item-text {
  display: block;
  padding: 0.5rem 1.25rem;
}

.dropdown-item-text,.c-footer {
  color: #4f5d73;
}

.c-footer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 0 0 50px;
  flex: 0 0 50px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  height: 50px;
  padding: 0 1rem;
  background: #ebedef;
  border-top: 1px solid #d8dbe0;
}

.c-footer[class*="bg-"] {
  border-color: rgba(0, 0, 21, 0.1);
}

.c-footer.c-footer-fixed {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.c-footer.c-footer-dark {
  color: #fff;
  background: #636f83;
}

.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  background-clip: padding-box;
  border: 1px solid;
  color: #768192;
  background-color: #fff;
  border-color: #d8dbe0;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}

.form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

.form-control:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #768192;
}

.form-control:focus {
  color: #768192;
  background-color: #fff;
  border-color: #958bef;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

.form-control::-webkit-input-placeholder {
  color: #8a93a2;
  opacity: 1;
}

.form-control::-moz-placeholder {
  color: #8a93a2;
  opacity: 1;
}

.form-control:-ms-input-placeholder {
  color: #8a93a2;
  opacity: 1;
}

.form-control::-ms-input-placeholder {
  color: #8a93a2;
  opacity: 1;
}

.form-control::placeholder {
  color: #8a93a2;
  opacity: 1;
}

.form-control:disabled, .form-control[readonly] {
  background-color: #d8dbe0;
  opacity: 1;
}

select.form-control:focus::-ms-value {
  color: #768192;
  background-color: #fff;
}

.form-control-file,
.form-control-range {
  display: block;
  width: 100%;
}

.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.09375rem;
  line-height: 1.5;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.765625rem;
  line-height: 1.5;
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.375rem 0;
  margin-bottom: 0;
  font-size: 0.875rem;
  line-height: 1.5;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
  color: #4f5d73;
}

.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.form-control-lg {
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

select.form-control[size], select.form-control[multiple],textarea.form-control {
  height: auto;
}

.form-group {
  margin-bottom: 1rem;
}

.form-text {
  display: block;
  margin-top: 0.25rem;
}

.form-row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}

.form-row > .col,
.form-row > [class*="col-"] {
  padding-right: 5px;
  padding-left: 5px;
}

.form-check {
  position: relative;
  display: block;
}

html:not([dir="rtl"]) .form-check {
  padding-left: 1.25rem;
}

*[dir="rtl"] .form-check {
  padding-right: 1.25rem;
}

.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
}

html:not([dir="rtl"]) .form-check-input {
  margin-left: -1.25rem;
}

*[dir="rtl"] .form-check-input {
  margin-right: -1.25rem;
}

.form-check-input[disabled] ~ .form-check-label,
.form-check-input:disabled ~ .form-check-label {
  color: #768192;
}

.form-check-label {
  margin-bottom: 0;
}

.form-check-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
  align-items: center;
}

html:not([dir="rtl"]) .form-check-inline {
  padding-left: 0;
  margin-right: 0.75rem;
}

*[dir="rtl"] .form-check-inline {
  padding-right: 0;
  margin-left: 0.75rem;
}

.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
}

html:not([dir="rtl"]) .form-check-inline .form-check-input {
  margin-right: 0.3125rem;
  margin-left: 0;
}

*[dir="rtl"] .form-check-inline .form-check-input {
  margin-right: 0;
  margin-left: 0.3125rem;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #2eb85c;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: .1rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(46, 184, 92, 0.9);
  border-radius: 0.25rem;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #2eb85c;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%232eb85c' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

html:not([dir="rtl"]) .was-validated .form-control:valid, html:not([dir="rtl"]) .form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: right calc(0.375em + 0.1875rem) center;
}

*[dir="rtl"] .was-validated .form-control:valid, *[dir="rtl"] .form-control.is-valid {
  padding-left: calc(1.5em + 0.75rem);
  background-position: left calc(0.375em + 0.1875rem) center;
}

.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #2eb85c;
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.25);
}

html:not([dir="rtl"]) .was-validated textarea.form-control:valid, html:not([dir="rtl"]) textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

*[dir="rtl"] .was-validated textarea.form-control:valid, *[dir="rtl"] textarea.form-control.is-valid {
  padding-left: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) left calc(0.375em + 0.1875rem);
}

.was-validated .custom-select:valid, .custom-select.is-valid {
  border-color: #2eb85c;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23636f83' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%232eb85c' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

html:not([dir="rtl"]) .was-validated .custom-select:valid, html:not([dir="rtl"]) .custom-select.is-valid {
  padding-right: calc(0.75em + 2.3125rem);
}

*[dir="rtl"] .was-validated .custom-select:valid, *[dir="rtl"] .custom-select.is-valid {
  padding-left: calc(0.75em + 2.3125rem);
}

.was-validated .custom-select:valid:focus, .custom-select.is-valid:focus {
  border-color: #2eb85c;
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.25);
}

.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #2eb85c;
}

.was-validated .form-check-input:valid ~ .valid-feedback,
.was-validated .form-check-input:valid ~ .valid-tooltip, .form-check-input.is-valid ~ .valid-feedback,
.form-check-input.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .custom-control-input:valid ~ .custom-control-label, .custom-control-input.is-valid ~ .custom-control-label {
  color: #2eb85c;
}

.was-validated .custom-control-input:valid ~ .custom-control-label::before, .custom-control-input.is-valid ~ .custom-control-label::before {
  border-color: #2eb85c;
}

.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .custom-control-input.is-valid:checked ~ .custom-control-label::before {
  border-color: #48d176;
  background-color: #48d176;
}

.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .custom-control-input.is-valid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.25);
}

.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before,.was-validated .custom-file-input:valid ~ .custom-file-label, .custom-file-input.is-valid ~ .custom-file-label {
  border-color: #2eb85c;
}

.was-validated .custom-file-input:valid:focus ~ .custom-file-label, .custom-file-input.is-valid:focus ~ .custom-file-label {
  border-color: #2eb85c;
  box-shadow: 0 0 0 0.2rem rgba(46, 184, 92, 0.25);
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #e55353;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: .1rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(229, 83, 83, 0.9);
  border-radius: 0.25rem;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #e55353;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23e55353' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23e55353' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

html:not([dir="rtl"]) .was-validated .form-control:invalid, html:not([dir="rtl"]) .form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: right calc(0.375em + 0.1875rem) center;
}

*[dir="rtl"] .was-validated .form-control:invalid, *[dir="rtl"] .form-control.is-invalid {
  padding-left: calc(1.5em + 0.75rem);
  background-position: left calc(0.375em + 0.1875rem) center;
}

.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #e55353;
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.25);
}

html:not([dir="rtl"]) .was-validated textarea.form-control:invalid, html:not([dir="rtl"]) textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

*[dir="rtl"] .was-validated textarea.form-control:invalid, *[dir="rtl"] textarea.form-control.is-invalid {
  padding-left: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) left calc(0.375em + 0.1875rem);
}

.was-validated .custom-select:invalid, .custom-select.is-invalid {
  border-color: #e55353;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23636f83' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23e55353' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23e55353' stroke='none'/%3e%3c/svg%3e") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

html:not([dir="rtl"]) .was-validated .custom-select:invalid, html:not([dir="rtl"]) .custom-select.is-invalid {
  padding-right: calc(0.75em + 2.3125rem);
}

*[dir="rtl"] .was-validated .custom-select:invalid, *[dir="rtl"] .custom-select.is-invalid {
  padding-left: calc(0.75em + 2.3125rem);
}

.was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus {
  border-color: #e55353;
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.25);
}

.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #e55353;
}

.was-validated .form-check-input:invalid ~ .invalid-feedback,
.was-validated .form-check-input:invalid ~ .invalid-tooltip, .form-check-input.is-invalid ~ .invalid-feedback,
.form-check-input.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .custom-control-input:invalid ~ .custom-control-label, .custom-control-input.is-invalid ~ .custom-control-label {
  color: #e55353;
}

.was-validated .custom-control-input:invalid ~ .custom-control-label::before, .custom-control-input.is-invalid ~ .custom-control-label::before {
  border-color: #e55353;
}

.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .custom-control-input.is-invalid:checked ~ .custom-control-label::before {
  border-color: #ec7f7f;
  background-color: #ec7f7f;
}

.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .custom-control-input.is-invalid:focus ~ .custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.25);
}

.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before,.was-validated .custom-file-input:invalid ~ .custom-file-label, .custom-file-input.is-invalid ~ .custom-file-label {
  border-color: #e55353;
}

.was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .custom-file-input.is-invalid:focus ~ .custom-file-label {
  border-color: #e55353;
  box-shadow: 0 0 0 0.2rem rgba(229, 83, 83, 0.25);
}

.form-inline {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  -ms-flex-align: center;
  align-items: center;
}

.form-inline .form-check {
  width: 100%;
}

@media (min-width: 576px) {
  .form-inline label {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .form-inline label,.form-inline .form-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 0;
  }
  .form-inline .form-group {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -ms-flex-align: center;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-plaintext {
    display: inline-block;
  }
  .form-inline .input-group,
  .form-inline .custom-select {
    width: auto;
  }
  .form-inline .form-check {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: auto;
  }
  html:not([dir="rtl"]) .form-inline .form-check {
    padding-left: 0;
  }
  *[dir="rtl"] .form-inline .form-check {
    padding-right: 0;
  }
  .form-inline .form-check-input {
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-top: 0;
  }
  html:not([dir="rtl"]) .form-inline .form-check-input {
    margin-right: 0.25rem;
    margin-left: 0;
  }
  *[dir="rtl"] .form-inline .form-check-input {
    margin-right: 0;
    margin-left: 0.25rem;
  }
  .form-inline .custom-control {
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

.container-fluid, .container-sm, .container-md, .container-lg, .container-xl {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container, .container-sm {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container, .container-sm, .container-md {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container, .container-sm, .container-md, .container-lg {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container, .container-sm, .container-md, .container-lg, .container-xl {
    max-width: 1140px;
  }
}

.row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col,
.col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm,
.col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md,
.col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg,
.col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 100%;
}

.row-cols-1 > * {
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.row-cols-2 > * {
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
}

.row-cols-3 > * {
  -ms-flex: 0 0 33.33333333%;
  flex: 0 0 33.33333333%;
  max-width: 33.33333333%;
}

.row-cols-4 > * {
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
}

.row-cols-5 > * {
  -ms-flex: 0 0 20%;
  flex: 0 0 20%;
  max-width: 20%;
}

.row-cols-6 > * {
  -ms-flex: 0 0 16.66666667%;
  flex: 0 0 16.66666667%;
  max-width: 16.66666667%;
}

.col-auto {
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.col-1 {
  -ms-flex: 0 0 8.33333333%;
  flex: 0 0 8.33333333%;
  max-width: 8.33333333%;
}

.col-2 {
  -ms-flex: 0 0 16.66666667%;
  flex: 0 0 16.66666667%;
  max-width: 16.66666667%;
}

.col-3 {
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
}

.col-4 {
  -ms-flex: 0 0 33.33333333%;
  flex: 0 0 33.33333333%;
  max-width: 33.33333333%;
}

.col-5 {
  -ms-flex: 0 0 41.66666667%;
  flex: 0 0 41.66666667%;
  max-width: 41.66666667%;
}

.col-6 {
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  -ms-flex: 0 0 58.33333333%;
  flex: 0 0 58.33333333%;
  max-width: 58.33333333%;
}

.col-8 {
  -ms-flex: 0 0 66.66666667%;
  flex: 0 0 66.66666667%;
  max-width: 66.66666667%;
}

.col-9 {
  -ms-flex: 0 0 75%;
  flex: 0 0 75%;
  max-width: 75%;
}

.col-10 {
  -ms-flex: 0 0 83.33333333%;
  flex: 0 0 83.33333333%;
  max-width: 83.33333333%;
}

.col-11 {
  -ms-flex: 0 0 91.66666667%;
  flex: 0 0 91.66666667%;
  max-width: 91.66666667%;
}

.col-12 {
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.order-first {
  -ms-flex-order: -1;
  order: -1;
}

.order-last {
  -ms-flex-order: 13;
  order: 13;
}

.order-0 {
  -ms-flex-order: 0;
  order: 0;
}

.order-1 {
  -ms-flex-order: 1;
  order: 1;
}

.order-2 {
  -ms-flex-order: 2;
  order: 2;
}

.order-3 {
  -ms-flex-order: 3;
  order: 3;
}

.order-4 {
  -ms-flex-order: 4;
  order: 4;
}

.order-5 {
  -ms-flex-order: 5;
  order: 5;
}

.order-6 {
  -ms-flex-order: 6;
  order: 6;
}

.order-7 {
  -ms-flex-order: 7;
  order: 7;
}

.order-8 {
  -ms-flex-order: 8;
  order: 8;
}

.order-9 {
  -ms-flex-order: 9;
  order: 9;
}

.order-10 {
  -ms-flex-order: 10;
  order: 10;
}

.order-11 {
  -ms-flex-order: 11;
  order: 11;
}

.order-12 {
  -ms-flex-order: 12;
  order: 12;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

@media (min-width: 576px) {
  .col-sm {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-sm-1 > * {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-sm-2 > * {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-sm-3 > * {
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .row-cols-sm-4 > * {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-sm-5 > * {
    -ms-flex: 0 0 20%;
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-sm-6 > * {
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-sm-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-sm-1 {
    -ms-flex: 0 0 8.33333333%;
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-sm-2 {
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-sm-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-sm-4 {
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-sm-5 {
    -ms-flex: 0 0 41.66666667%;
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-sm-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-sm-7 {
    -ms-flex: 0 0 58.33333333%;
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-sm-8 {
    -ms-flex: 0 0 66.66666667%;
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-sm-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-sm-10 {
    -ms-flex: 0 0 83.33333333%;
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-sm-11 {
    -ms-flex: 0 0 91.66666667%;
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-sm-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-sm-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .order-sm-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .order-sm-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .order-sm-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .order-sm-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .order-sm-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .order-sm-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .order-sm-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .order-sm-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .order-sm-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .order-sm-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .order-sm-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .order-sm-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .order-sm-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .order-sm-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
}

@media (min-width: 768px) {
  .col-md {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-md-1 > * {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-md-2 > * {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-md-3 > * {
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .row-cols-md-4 > * {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-md-5 > * {
    -ms-flex: 0 0 20%;
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-md-6 > * {
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-md-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-md-1 {
    -ms-flex: 0 0 8.33333333%;
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-md-2 {
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-md-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-md-4 {
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-md-5 {
    -ms-flex: 0 0 41.66666667%;
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-md-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-md-7 {
    -ms-flex: 0 0 58.33333333%;
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-md-8 {
    -ms-flex: 0 0 66.66666667%;
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-md-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-md-10 {
    -ms-flex: 0 0 83.33333333%;
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-md-11 {
    -ms-flex: 0 0 91.66666667%;
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-md-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-md-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .order-md-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .order-md-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .order-md-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .order-md-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .order-md-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .order-md-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .order-md-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .order-md-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .order-md-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .order-md-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .order-md-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .order-md-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .order-md-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .order-md-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .offset-md-11 {
    margin-left: 91.66666667%;
  }
}

@media (min-width: 992px) {
  .col-lg {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-lg-1 > * {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-lg-2 > * {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-lg-3 > * {
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .row-cols-lg-4 > * {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-lg-5 > * {
    -ms-flex: 0 0 20%;
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-lg-6 > * {
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-lg-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-lg-1 {
    -ms-flex: 0 0 8.33333333%;
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-lg-2 {
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-lg-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-lg-4 {
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-lg-5 {
    -ms-flex: 0 0 41.66666667%;
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-lg-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-lg-7 {
    -ms-flex: 0 0 58.33333333%;
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-lg-8 {
    -ms-flex: 0 0 66.66666667%;
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-lg-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-lg-10 {
    -ms-flex: 0 0 83.33333333%;
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-lg-11 {
    -ms-flex: 0 0 91.66666667%;
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-lg-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-lg-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .order-lg-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .order-lg-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .order-lg-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .order-lg-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .order-lg-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .order-lg-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .order-lg-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .order-lg-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .order-lg-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .order-lg-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .order-lg-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .order-lg-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .order-lg-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .order-lg-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
}

@media (min-width: 1200px) {
  .col-xl {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .row-cols-xl-1 > * {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .row-cols-xl-2 > * {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .row-cols-xl-3 > * {
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .row-cols-xl-4 > * {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .row-cols-xl-5 > * {
    -ms-flex: 0 0 20%;
    flex: 0 0 20%;
    max-width: 20%;
  }
  .row-cols-xl-6 > * {
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-xl-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .col-xl-1 {
    -ms-flex: 0 0 8.33333333%;
    flex: 0 0 8.33333333%;
    max-width: 8.33333333%;
  }
  .col-xl-2 {
    -ms-flex: 0 0 16.66666667%;
    flex: 0 0 16.66666667%;
    max-width: 16.66666667%;
  }
  .col-xl-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .col-xl-4 {
    -ms-flex: 0 0 33.33333333%;
    flex: 0 0 33.33333333%;
    max-width: 33.33333333%;
  }
  .col-xl-5 {
    -ms-flex: 0 0 41.66666667%;
    flex: 0 0 41.66666667%;
    max-width: 41.66666667%;
  }
  .col-xl-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .col-xl-7 {
    -ms-flex: 0 0 58.33333333%;
    flex: 0 0 58.33333333%;
    max-width: 58.33333333%;
  }
  .col-xl-8 {
    -ms-flex: 0 0 66.66666667%;
    flex: 0 0 66.66666667%;
    max-width: 66.66666667%;
  }
  .col-xl-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .col-xl-10 {
    -ms-flex: 0 0 83.33333333%;
    flex: 0 0 83.33333333%;
    max-width: 83.33333333%;
  }
  .col-xl-11 {
    -ms-flex: 0 0 91.66666667%;
    flex: 0 0 91.66666667%;
    max-width: 91.66666667%;
  }
  .col-xl-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .order-xl-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .order-xl-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .order-xl-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .order-xl-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .order-xl-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .order-xl-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .order-xl-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .order-xl-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .order-xl-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .order-xl-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .order-xl-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .order-xl-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .order-xl-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .order-xl-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .order-xl-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
}

.row.row-equal {
  margin-right: -15px;
  margin-left: -15px;
}

.row.row-equal,.row.row-equal [class*="col-"] {
  padding-right: 7.5px;
  padding-left: 7.5px;
}

.main .container-fluid, .main .container-sm, .main .container-md, .main .container-lg, .main .container-xl {
  padding: 0 30px;
}

.c-header {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  min-height: 56px;
  background: #fff;
  border-bottom: 1px solid #d8dbe0;
}

.c-header[class*="bg-"] {
  border-color: rgba(0, 0, 21, 0.1);
}

.c-header.c-header-fixed {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}

.c-header .c-subheader {
  border-bottom: 0;
  margin-top: -1px;
  border-top: 1px solid #d8dbe0;
}

.c-header-brand {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: auto;
  min-height: 56px;
  transition: width 0.25s;
}

.c-header-brand.c-header-brand-center {
  position: absolute;
  top: 56px;
  -webkit-transform: translate(-50%, -100%);
  transform: translate(-50%, -100%);
}

html:not([dir="rtl"]) .c-header-brand.c-header-brand-center {
  left: 50%;
}

*[dir="rtl"] .c-header-brand.c-header-brand-center {
  right: 50%;
}

@media (max-width: 575.98px) {
  .c-header-brand.c-header-brand-xs-down-center {
    position: absolute;
    top: 56px;
    -webkit-transform: translate(-50%, -100%);
    transform: translate(-50%, -100%);
  }
  html:not([dir="rtl"]) .c-header-brand.c-header-brand-xs-down-center {
    left: 50%;
  }
  *[dir="rtl"] .c-header-brand.c-header-brand-xs-down-center {
    right: 50%;
  }
}

.c-header-brand.c-header-brand-xs-up-center {
  position: absolute;
  top: 56px;
  -webkit-transform: translate(-50%, -100%);
  transform: translate(-50%, -100%);
}

html:not([dir="rtl"]) .c-header-brand.c-header-brand-xs-up-center {
  left: 50%;
}

*[dir="rtl"] .c-header-brand.c-header-brand-xs-up-center {
  right: 50%;
}

@media (max-width: 767.98px) {
  .c-header-brand.c-header-brand-sm-down-center {
    position: absolute;
    top: 56px;
    -webkit-transform: translate(-50%, -100%);
    transform: translate(-50%, -100%);
  }
  html:not([dir="rtl"]) .c-header-brand.c-header-brand-sm-down-center {
    left: 50%;
  }
  *[dir="rtl"] .c-header-brand.c-header-brand-sm-down-center {
    right: 50%;
  }
}

@media (min-width: 576px) {
  .c-header-brand.c-header-brand-sm-up-center {
    position: absolute;
    top: 56px;
    -webkit-transform: translate(-50%, -100%);
    transform: translate(-50%, -100%);
  }
  html:not([dir="rtl"]) .c-header-brand.c-header-brand-sm-up-center {
    left: 50%;
  }
  *[dir="rtl"] .c-header-brand.c-header-brand-sm-up-center {
    right: 50%;
  }
}

@media (max-width: 991.98px) {
  .c-header-brand.c-header-brand-md-down-center {
    position: absolute;
    top: 56px;
    -webkit-transform: translate(-50%, -100%);
    transform: translate(-50%, -100%);
  }
  html:not([dir="rtl"]) .c-header-brand.c-header-brand-md-down-center {
    left: 50%;
  }
  *[dir="rtl"] .c-header-brand.c-header-brand-md-down-center {
    right: 50%;
  }
}

@media (min-width: 768px) {
  .c-header-brand.c-header-brand-md-up-center {
    position: absolute;
    top: 56px;
    -webkit-transform: translate(-50%, -100%);
    transform: translate(-50%, -100%);
  }
  html:not([dir="rtl"]) .c-header-brand.c-header-brand-md-up-center {
    left: 50%;
  }
  *[dir="rtl"] .c-header-brand.c-header-brand-md-up-center {
    right: 50%;
  }
}

@media (max-width: 1199.98px) {
  .c-header-brand.c-header-brand-lg-down-center {
    position: absolute;
    top: 56px;
    -webkit-transform: translate(-50%, -100%);
    transform: translate(-50%, -100%);
  }
  html:not([dir="rtl"]) .c-header-brand.c-header-brand-lg-down-center {
    left: 50%;
  }
  *[dir="rtl"] .c-header-brand.c-header-brand-lg-down-center {
    right: 50%;
  }
}

@media (min-width: 992px) {
  .c-header-brand.c-header-brand-lg-up-center {
    position: absolute;
    top: 56px;
    -webkit-transform: translate(-50%, -100%);
    transform: translate(-50%, -100%);
  }
  html:not([dir="rtl"]) .c-header-brand.c-header-brand-lg-up-center {
    left: 50%;
  }
  *[dir="rtl"] .c-header-brand.c-header-brand-lg-up-center {
    right: 50%;
  }
}

.c-header-brand.c-header-brand-xl-down-center {
  position: absolute;
  top: 56px;
  -webkit-transform: translate(-50%, -100%);
  transform: translate(-50%, -100%);
}

html:not([dir="rtl"]) .c-header-brand.c-header-brand-xl-down-center {
  left: 50%;
}

*[dir="rtl"] .c-header-brand.c-header-brand-xl-down-center {
  right: 50%;
}

@media (min-width: 1200px) {
  .c-header-brand.c-header-brand-xl-up-center {
    position: absolute;
    top: 56px;
    -webkit-transform: translate(-50%, -100%);
    transform: translate(-50%, -100%);
  }
  html:not([dir="rtl"]) .c-header-brand.c-header-brand-xl-up-center {
    left: 50%;
  }
  *[dir="rtl"] .c-header-brand.c-header-brand-xl-up-center {
    right: 50%;
  }
}

.c-header-toggler {
  min-width: 50px;
  font-size: 1.09375rem;
  background-color: transparent;
  border: 0;
  border-radius: 0.25rem;
}

.c-header-toggler:hover, .c-header-toggler:focus {
  text-decoration: none;
}

.c-header-toggler:not(:disabled):not(.c-disabled) {
  cursor: pointer;
}

.c-header-toggler-icon {
  display: block;
  height: 1.3671875rem;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
}

.c-header-nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-align: center;
  align-items: center;
  min-height: 56px;
  padding: 0;
  margin-bottom: 0;
  list-style: none;
}

.c-header-nav .c-header-nav-item {
  position: relative;
}

.c-header-nav .c-header-nav-btn {
  background-color: transparent;
  border: 1px solid transparent;
}

.c-header-nav .c-header-nav-link,
.c-header-nav .c-header-nav-btn {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

.c-header-nav .c-header-nav-link .badge,
.c-header-nav .c-header-nav-btn .badge {
  position: absolute;
  top: 50%;
  margin-top: -16px;
}

html:not([dir="rtl"]) .c-header-nav .c-header-nav-link .badge, html:not([dir="rtl"])
.c-header-nav .c-header-nav-btn .badge {
  left: 50%;
  margin-left: 0;
}

*[dir="rtl"] .c-header-nav .c-header-nav-link .badge, *[dir="rtl"]
.c-header-nav .c-header-nav-btn .badge {
  right: 50%;
  margin-right: 0;
}

.c-header-nav .c-header-nav-link:hover,
.c-header-nav .c-header-nav-btn:hover {
  text-decoration: none;
}

.c-header-nav .dropdown-item {
  min-width: 180px;
}

.c-header.c-header-dark {
  background: #3c4b64;
  border-bottom: 1px solid #636f83;
}

.c-header.c-header-dark .c-subheader {
  margin-top: -1px;
  border-top: 1px solid #636f83;
}

.c-header.c-header-dark .c-header-brand {
  color: #fff;
  background-color: transparent;
}

.c-header.c-header-dark .c-header-brand:hover, .c-header.c-header-dark .c-header-brand:focus {
  color: #fff;
}

.c-header.c-header-dark .c-header-nav .c-header-nav-link,
.c-header.c-header-dark .c-header-nav .c-header-nav-btn {
  color: rgba(255, 255, 255, 0.75);
}

.c-header.c-header-dark .c-header-nav .c-header-nav-link:hover, .c-header.c-header-dark .c-header-nav .c-header-nav-link:focus,
.c-header.c-header-dark .c-header-nav .c-header-nav-btn:hover,
.c-header.c-header-dark .c-header-nav .c-header-nav-btn:focus {
  color: rgba(255, 255, 255, 0.9);
}

.c-header.c-header-dark .c-header-nav .c-header-nav-link.c-disabled,
.c-header.c-header-dark .c-header-nav .c-header-nav-btn.c-disabled {
  color: rgba(255, 255, 255, 0.25);
}

.c-header.c-header-dark .c-header-nav .c-show > .c-header-nav-link,
.c-header.c-header-dark .c-header-nav .c-active > .c-header-nav-link,
.c-header.c-header-dark .c-header-nav .c-header-nav-link.c-show,
.c-header.c-header-dark .c-header-nav .c-header-nav-link.c-active {
  color: #fff;
}

.c-header.c-header-dark .c-header-toggler {
  color: rgba(255, 255, 255, 0.75);
  border-color: rgba(255, 255, 255, 0.1);
}

.c-header.c-header-dark .c-header-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.75)' stroke-width='2.25' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

.c-header.c-header-dark .c-header-toggler-icon:hover {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.9)' stroke-width='2.25' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

.c-header.c-header-dark .c-header-text {
  color: rgba(255, 255, 255, 0.75);
}

.c-header.c-header-dark .c-header-text a,.c-header.c-header-dark .c-header-text a:hover, .c-header.c-header-dark .c-header-text a:focus {
  color: #fff;
}

.c-header .c-header-brand {
  color: #4f5d73;
  background-color: transparent;
}

.c-header .c-header-brand:hover, .c-header .c-header-brand:focus {
  color: #3a4555;
}

.c-header .c-header-nav .c-header-nav-link,
.c-header .c-header-nav .c-header-nav-btn {
  color: rgba(0, 0, 21, 0.5);
}

.c-header .c-header-nav .c-header-nav-link:hover, .c-header .c-header-nav .c-header-nav-link:focus,
.c-header .c-header-nav .c-header-nav-btn:hover,
.c-header .c-header-nav .c-header-nav-btn:focus {
  color: rgba(0, 0, 21, 0.7);
}

.c-header .c-header-nav .c-header-nav-link.c-disabled,
.c-header .c-header-nav .c-header-nav-btn.c-disabled {
  color: rgba(0, 0, 21, 0.3);
}

.c-header .c-header-nav .c-show > .c-header-nav-link,
.c-header .c-header-nav .c-active > .c-header-nav-link,
.c-header .c-header-nav .c-header-nav-link.c-show,
.c-header .c-header-nav .c-header-nav-link.c-active {
  color: rgba(0, 0, 21, 0.9);
}

.c-header .c-header-toggler {
  color: rgba(0, 0, 21, 0.5);
  border-color: rgba(0, 0, 21, 0.1);
}

.c-header .c-header-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(0, 0, 21, 0.5)' stroke-width='2.25' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

.c-header .c-header-toggler-icon:hover {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(0, 0, 21, 0.7)' stroke-width='2.25' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

.c-header .c-header-text {
  color: rgba(0, 0, 21, 0.5);
}

.c-header .c-header-text a,.c-header .c-header-text a:hover, .c-header .c-header-text a:focus {
  color: rgba(0, 0, 21, 0.9);
}

.c-icon {
  display: inline-block;
  color: inherit;
  text-align: center;
  fill: currentColor;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size) {
  width: 1rem;
  height: 1rem;
  font-size: 1rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-2xl {
  width: 2rem;
  height: 2rem;
  font-size: 2rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-3xl {
  width: 3rem;
  height: 3rem;
  font-size: 3rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-4xl {
  width: 4rem;
  height: 4rem;
  font-size: 4rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-5xl {
  width: 5rem;
  height: 5rem;
  font-size: 5rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-6xl {
  width: 6rem;
  height: 6rem;
  font-size: 6rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-7xl {
  width: 7rem;
  height: 7rem;
  font-size: 7rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-8xl {
  width: 8rem;
  height: 8rem;
  font-size: 8rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-9xl {
  width: 9rem;
  height: 9rem;
  font-size: 9rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-xl {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 1.5rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-lg {
  width: 1.25rem;
  height: 1.25rem;
  font-size: 1.25rem;
}

.c-icon:not(.c-icon-c-s):not(.c-icon-custom-size).c-icon-sm {
  width: 0.875rem;
  height: 0.875rem;
  font-size: 0.875rem;
}

.input-group {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}

.input-group > .form-control,
.input-group > .form-control-plaintext,
.input-group > .custom-select,
.input-group > .custom-file {
  position: relative;
  -ms-flex: 1 1 0%;
  flex: 1 1 0%;
  min-width: 0;
  margin-bottom: 0;
}

html:not([dir="rtl"]) .input-group > .form-control + .form-control, html:not([dir="rtl"])
.input-group > .form-control + .custom-select, html:not([dir="rtl"])
.input-group > .form-control + .custom-file, html:not([dir="rtl"])
.input-group > .form-control-plaintext + .form-control, html:not([dir="rtl"])
.input-group > .form-control-plaintext + .custom-select, html:not([dir="rtl"])
.input-group > .form-control-plaintext + .custom-file, html:not([dir="rtl"])
.input-group > .custom-select + .form-control, html:not([dir="rtl"])
.input-group > .custom-select + .custom-select, html:not([dir="rtl"])
.input-group > .custom-select + .custom-file, html:not([dir="rtl"])
.input-group > .custom-file + .form-control, html:not([dir="rtl"])
.input-group > .custom-file + .custom-select, html:not([dir="rtl"])
.input-group > .custom-file + .custom-file {
  margin-left: -1px;
}

*[dir="rtl"] .input-group > .form-control + .form-control, *[dir="rtl"]
.input-group > .form-control + .custom-select, *[dir="rtl"]
.input-group > .form-control + .custom-file, *[dir="rtl"]
.input-group > .form-control-plaintext + .form-control, *[dir="rtl"]
.input-group > .form-control-plaintext + .custom-select, *[dir="rtl"]
.input-group > .form-control-plaintext + .custom-file, *[dir="rtl"]
.input-group > .custom-select + .form-control, *[dir="rtl"]
.input-group > .custom-select + .custom-select, *[dir="rtl"]
.input-group > .custom-select + .custom-file, *[dir="rtl"]
.input-group > .custom-file + .form-control, *[dir="rtl"]
.input-group > .custom-file + .custom-select, *[dir="rtl"]
.input-group > .custom-file + .custom-file {
  margin-right: -1px;
}

.input-group > .form-control:focus,
.input-group > .custom-select:focus,
.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label {
  z-index: 3;
}

.input-group > .custom-file .custom-file-input:focus {
  z-index: 4;
}

html:not([dir="rtl"]) .input-group > .form-control:not(:last-child), html:not([dir="rtl"])
.input-group > .custom-select:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

*[dir="rtl"] .input-group > .form-control:not(:last-child), *[dir="rtl"]
.input-group > .custom-select:not(:last-child),html:not([dir="rtl"]) .input-group > .form-control:not(:first-child), html:not([dir="rtl"])
.input-group > .custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

*[dir="rtl"] .input-group > .form-control:not(:first-child), *[dir="rtl"]
.input-group > .custom-select:not(:first-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .custom-file {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
}

html:not([dir="rtl"]) .input-group > .custom-file:not(:last-child) .custom-file-label, html:not([dir="rtl"])
.input-group > .custom-file:not(:last-child) .custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

*[dir="rtl"] .input-group > .custom-file:not(:last-child) .custom-file-label, *[dir="rtl"]
.input-group > .custom-file:not(:last-child) .custom-file-label::after,html:not([dir="rtl"]) .input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

*[dir="rtl"] .input-group > .custom-file:not(:first-child) .custom-file-label {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group-prepend,
.input-group-append {
  display: -ms-flexbox;
  display: flex;
}

.input-group-prepend .btn,
.input-group-append .btn {
  position: relative;
  z-index: 2;
}

.input-group-prepend .btn:focus,
.input-group-append .btn:focus {
  z-index: 3;
}

html:not([dir="rtl"]) .input-group-prepend .btn + .btn, html:not([dir="rtl"])
.input-group-prepend .btn + .input-group-text, html:not([dir="rtl"])
.input-group-prepend .input-group-text + .input-group-text, html:not([dir="rtl"])
.input-group-prepend .input-group-text + .btn, html:not([dir="rtl"])
.input-group-append .btn + .btn, html:not([dir="rtl"])
.input-group-append .btn + .input-group-text, html:not([dir="rtl"])
.input-group-append .input-group-text + .input-group-text, html:not([dir="rtl"])
.input-group-append .input-group-text + .btn {
  margin-left: -1px;
}

*[dir="rtl"] .input-group-prepend .btn + .btn, *[dir="rtl"]
.input-group-prepend .btn + .input-group-text, *[dir="rtl"]
.input-group-prepend .input-group-text + .input-group-text, *[dir="rtl"]
.input-group-prepend .input-group-text + .btn, *[dir="rtl"]
.input-group-append .btn + .btn, *[dir="rtl"]
.input-group-append .btn + .input-group-text, *[dir="rtl"]
.input-group-append .input-group-text + .input-group-text, *[dir="rtl"]
.input-group-append .input-group-text + .btn {
  margin-right: -1px;
}

.input-group-prepend {
  white-space: nowrap;
  vertical-align: middle;
}

html:not([dir="rtl"]) .input-group-prepend {
  margin-right: -1px;
}

*[dir="rtl"] .input-group-prepend {
  margin-left: -1px;
}

.input-group-append {
  white-space: nowrap;
  vertical-align: middle;
}

html:not([dir="rtl"]) .input-group-append {
  margin-left: -1px;
}

*[dir="rtl"] .input-group-append {
  margin-right: -1px;
}

.input-group-text {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
  border: 1px solid;
  border-radius: 0.25rem;
  color: #768192;
  background-color: #ebedef;
  border-color: #d8dbe0;
}

.input-group-text input[type="radio"],
.input-group-text input[type="checkbox"] {
  margin-top: 0;
}

.input-group-lg > .form-control:not(textarea),
.input-group-lg > .custom-select {
  height: calc(1.5em + 1rem + 2px);
}

.input-group-lg > .form-control,
.input-group-lg > .custom-select,
.input-group-lg > .input-group-prepend > .input-group-text,
.input-group-lg > .input-group-append > .input-group-text,
.input-group-lg > .input-group-prepend > .btn,
.input-group-lg > .input-group-append > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.input-group-sm > .form-control:not(textarea),
.input-group-sm > .custom-select {
  height: calc(1.5em + 0.5rem + 2px);
}

.input-group-sm > .form-control,
.input-group-sm > .custom-select,
.input-group-sm > .input-group-prepend > .input-group-text,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-append > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

html:not([dir="rtl"]) .input-group-lg > .custom-select, html:not([dir="rtl"])
.input-group-sm > .custom-select {
  padding-right: 1.75rem;
}

*[dir="rtl"] .input-group-lg > .custom-select, *[dir="rtl"]
.input-group-sm > .custom-select {
  padding-left: 1.75rem;
}

html:not([dir="rtl"]) .input-group > .input-group-prepend > .btn, html:not([dir="rtl"])
.input-group > .input-group-prepend > .input-group-text, html:not([dir="rtl"])
.input-group > .input-group-append:not(:last-child) > .btn, html:not([dir="rtl"])
.input-group > .input-group-append:not(:last-child) > .input-group-text, html:not([dir="rtl"])
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle), html:not([dir="rtl"])
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

*[dir="rtl"] .input-group > .input-group-prepend > .btn, *[dir="rtl"]
.input-group > .input-group-prepend > .input-group-text, *[dir="rtl"]
.input-group > .input-group-append:not(:last-child) > .btn, *[dir="rtl"]
.input-group > .input-group-append:not(:last-child) > .input-group-text, *[dir="rtl"]
.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle), *[dir="rtl"]
.input-group > .input-group-append:last-child > .input-group-text:not(:last-child),html:not([dir="rtl"]) .input-group > .input-group-append > .btn, html:not([dir="rtl"])
.input-group > .input-group-append > .input-group-text, html:not([dir="rtl"])
.input-group > .input-group-prepend:not(:first-child) > .btn, html:not([dir="rtl"])
.input-group > .input-group-prepend:not(:first-child) > .input-group-text, html:not([dir="rtl"])
.input-group > .input-group-prepend:first-child > .btn:not(:first-child), html:not([dir="rtl"])
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

*[dir="rtl"] .input-group > .input-group-append > .btn, *[dir="rtl"]
.input-group > .input-group-append > .input-group-text, *[dir="rtl"]
.input-group > .input-group-prepend:not(:first-child) > .btn, *[dir="rtl"]
.input-group > .input-group-prepend:not(:first-child) > .input-group-text, *[dir="rtl"]
.input-group > .input-group-prepend:first-child > .btn:not(:first-child), *[dir="rtl"]
.input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.img-fluid,.img-thumbnail {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: #ebedef;
  border: 1px solid #c4c9d0;
  border-radius: 0.25rem;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 90%;
  color: #8a93a2;
}

.jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  border-radius: 0.3rem;
  background-color: #d8dbe0;
}

@media (min-width: 576px) {
  .jumbotron {
    padding: 4rem 2rem;
  }
}

.jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}

.list-group {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-bottom: 0;
}

html:not([dir="rtl"]) .list-group {
  padding-left: 0;
}

*[dir="rtl"] .list-group {
  padding-right: 0;
}

.list-group-item-action {
  width: 100%;
  text-align: inherit;
  color: #768192;
}

.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  text-decoration: none;
  color: #768192;
  background-color: #ebedef;
}

.list-group-item-action:active {
  color: #4f5d73;
  background-color: #321fdb;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  border: 1px solid;
  background-color: inherit;
  border-color: rgba(0, 0, 21, 0.125);
}

.list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.list-group-item:last-child {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.list-group-item.disabled, .list-group-item:disabled {
  pointer-events: none;
  color: #8a93a2;
  background-color: inherit;
}

.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #321fdb;
  border-color: #321fdb;
}

.list-group-item + .list-group-item {
  border-top-width: 0;
}

.list-group-item + .list-group-item.active {
  margin-top: -1px;
  border-top-width: 1px;
}

.list-group-horizontal {
  -ms-flex-direction: row;
  flex-direction: row;
}

.list-group-horizontal .list-group-item:first-child {
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
}

.list-group-horizontal .list-group-item:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
}

.list-group-horizontal .list-group-item.active {
  margin-top: 0;
}

.list-group-horizontal .list-group-item + .list-group-item {
  border-top-width: 1px;
  border-left-width: 0;
}

.list-group-horizontal .list-group-item + .list-group-item.active {
  margin-left: -1px;
  border-left-width: 1px;
}

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-sm .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-sm .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

@media (min-width: 768px) {
  .list-group-horizontal-md {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-md .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-md .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

@media (min-width: 992px) {
  .list-group-horizontal-lg {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-lg .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-lg .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-xl .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xl .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}

.list-group-flush .list-group-item {
  border-right-width: 0;
  border-left-width: 0;
  border-radius: 0;
}

.list-group-flush .list-group-item:first-child {
  border-top-width: 0;
}

.list-group-flush:last-child .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-primary {
  color: #1a107c;
  background-color: #c6c0f5;
}

.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
  color: #1a107c;
  background-color: #b2aaf2;
}

.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #1a107c;
  border-color: #1a107c;
}

.list-group-item-secondary {
  color: #6b6d7a;
  background-color: #f1f2f4;
}

.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus {
  color: #6b6d7a;
  background-color: #e3e5e9;
}

.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #6b6d7a;
  border-color: #6b6d7a;
}

.list-group-item-success {
  color: #18603a;
  background-color: #c4ebd1;
}

.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus {
  color: #18603a;
  background-color: #b1e5c2;
}

.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #18603a;
  border-color: #18603a;
}

.list-group-item-info {
  color: #1b508f;
  background-color: #c6e2ff;
}

.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus {
  color: #1b508f;
  background-color: #add5ff;
}

.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #1b508f;
  border-color: #1b508f;
}

.list-group-item-warning {
  color: #815c15;
  background-color: #fde9bd;
}

.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus {
  color: #815c15;
  background-color: #fce1a4;
}

.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #815c15;
  border-color: #815c15;
}

.list-group-item-danger {
  color: #772b35;
  background-color: #f8cfcf;
}

.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {
  color: #772b35;
  background-color: #f5b9b9;
}

.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #772b35;
  border-color: #772b35;
}

.list-group-item-light {
  color: #7a7b86;
  background-color: #f9fafb;
}

.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus {
  color: #7a7b86;
  background-color: #eaedf1;
}

.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #7a7b86;
  border-color: #7a7b86;
}

.list-group-item-dark {
  color: #333a4e;
  background-color: #d3d7dc;
}

.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus {
  color: #333a4e;
  background-color: #c5cad1;
}

.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #333a4e;
  border-color: #333a4e;
}

.list-group-accent .list-group-item {
  margin-bottom: 1px;
  border-top: 0;
  border-right: 0;
  border-bottom: 0;
  border-radius: 0;
}

.list-group-accent .list-group-item.list-group-item-divider {
  position: relative;
}

.list-group-accent .list-group-item.list-group-item-divider::before {
  position: absolute;
  bottom: -1px;
  width: 90%;
  height: 1px;
  content: "";
  background-color: rgba(0, 0, 21, 0.125);
}

html:not([dir="rtl"]) .list-group-accent .list-group-item.list-group-item-divider::before {
  left: 5%;
}

*[dir="rtl"] .list-group-accent .list-group-item.list-group-item-divider::before {
  right: 5%;
}

.list-group-accent .list-group-item-accent-primary {
  border-left: 4px solid #321fdb;
}

.list-group-accent .list-group-item-accent-secondary {
  border-left: 4px solid #ced2d8;
}

.list-group-accent .list-group-item-accent-success {
  border-left: 4px solid #2eb85c;
}

.list-group-accent .list-group-item-accent-info {
  border-left: 4px solid #39f;
}

.list-group-accent .list-group-item-accent-warning {
  border-left: 4px solid #f9b115;
}

.list-group-accent .list-group-item-accent-danger {
  border-left: 4px solid #e55353;
}

.list-group-accent .list-group-item-accent-light {
  border-left: 4px solid #ebedef;
}

.list-group-accent .list-group-item-accent-dark {
  border-left: 4px solid #636f83;
}

.media {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
}

.media-body {
  -ms-flex: 1;
  flex: 1;
}

.modal-open {
  overflow: hidden;
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}

.modal.fade .modal-dialog {
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translate(0, -50px);
  transform: translate(0, -50px);
}

@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}

.modal.show .modal-dialog {
  -webkit-transform: none;
  transform: none;
}

.modal.modal-static .modal-dialog {
  -webkit-transform: scale(1.02);
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  display: -ms-flexbox;
  display: flex;
  max-height: calc(100% - 1rem);
}

.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}

.modal-dialog-scrollable .modal-header,
.modal-dialog-scrollable .modal-footer {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  min-height: calc(100% - 1rem);
}

.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  content: "";
}

.modal-dialog-centered.modal-dialog-scrollable {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%;
}

.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}

.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}

.modal-content {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-clip: padding-box;
  border: 1px solid;
  border-radius: 0.3rem;
  outline: 0;
  background-color: #fff;
  border-color: rgba(0, 0, 21, 0.2);
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000015;
}

.modal-backdrop.fade {
  opacity: 0;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.modal-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: justify;
  justify-content: space-between;
  border-bottom: 1px solid;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
  border-color: #d8dbe0;
}

.modal-header,.modal-header .close {
  padding: 1rem 1rem;
}

html:not([dir="rtl"]) .modal-header .close {
  margin: -1rem -1rem -1rem auto;
}

*[dir="rtl"] .modal-header .close {
  margin: -1rem auto -1rem -1rem;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.modal-body {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
  border-color: #d8dbe0;
}

.modal-footer > * {
  margin: 0.25rem;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 3.5rem);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
  }
  .modal-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 800px;
  }
}

@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}

.modal-primary .modal-content {
  border-color: #321fdb;
}

.modal-primary .modal-header {
  color: #fff;
  background-color: #321fdb;
}

.modal-secondary .modal-content {
  border-color: #ced2d8;
}

.modal-secondary .modal-header {
  color: #fff;
  background-color: #ced2d8;
}

.modal-success .modal-content {
  border-color: #2eb85c;
}

.modal-success .modal-header {
  color: #fff;
  background-color: #2eb85c;
}

.modal-info .modal-content {
  border-color: #39f;
}

.modal-info .modal-header {
  color: #fff;
  background-color: #39f;
}

.modal-warning .modal-content {
  border-color: #f9b115;
}

.modal-warning .modal-header {
  color: #fff;
  background-color: #f9b115;
}

.modal-danger .modal-content {
  border-color: #e55353;
}

.modal-danger .modal-header {
  color: #fff;
  background-color: #e55353;
}

.modal-light .modal-content {
  border-color: #ebedef;
}

.modal-light .modal-header {
  color: #fff;
  background-color: #ebedef;
}

.modal-dark .modal-content {
  border-color: #636f83;
}

.modal-dark .modal-header {
  color: #fff;
  background-color: #636f83;
}

.nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-bottom: 0;
  list-style: none;
}

html:not([dir="rtl"]) .nav {
  padding-left: 0;
}

*[dir="rtl"] .nav {
  padding-right: 0;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}

.nav-link:hover, .nav-link:focus {
  text-decoration: none;
}

.nav-link.disabled {
  color: #8a93a2;
  pointer-events: none;
  cursor: default;
  color: #8a93a2;
}

.nav-tabs {
  border-bottom: 1px solid;
  border-color: #c4c9d0;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  border-color: #d8dbe0 #d8dbe0 #c4c9d0;
}

.nav-tabs .nav-link.disabled {
  background-color: transparent;
  border-color: transparent;
  color: #8a93a2;
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #768192;
  background-color: #ebedef;
  border-color: #c4c9d0 #c4c9d0 #ebedef;
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-tabs-boxed .nav-tabs {
  border: 0;
}

.nav-tabs-boxed .nav-tabs .nav-link.active {
  background-color: #fff;
  border-bottom-color: #fff;
}

.nav-tabs-boxed .tab-content {
  padding: 0.75rem 1.25rem;
  border: 1px solid;
  border-radius: 0 0.25rem 0.25rem 0.25rem;
  color: #768192;
  background-color: #fff;
  border-color: #d8dbe0;
}

.nav-tabs-boxed.nav-tabs-boxed-top-right .nav-tabs {
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.nav-tabs-boxed.nav-tabs-boxed-top-right .tab-content {
  border-radius: 0.25rem 0 0.25rem 0.25rem;
}

.nav-tabs-boxed.nav-tabs-boxed-left, .nav-tabs-boxed.nav-tabs-boxed-right {
  display: -ms-flexbox;
  display: flex;
}

.nav-tabs-boxed.nav-tabs-boxed-left .nav-item, .nav-tabs-boxed.nav-tabs-boxed-right .nav-item {
  z-index: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-bottom: 0;
}

*[dir="rtl"] .nav-tabs-boxed.nav-tabs-boxed-left {
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
}

.nav-tabs-boxed.nav-tabs-boxed-left .nav-item {
  margin-right: -1px;
}

.nav-tabs-boxed.nav-tabs-boxed-left .nav-link {
  border-radius: 0.25rem 0 0 0.25rem;
}

.nav-tabs-boxed.nav-tabs-boxed-left .nav-link.active {
  border-color: #d8dbe0 #fff #d8dbe0 #d8dbe0;
}

html:not([dir="rtl"]) .nav-tabs-boxed.nav-tabs-boxed-right {
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
}

*[dir="rtl"] .nav-tabs-boxed.nav-tabs-boxed-right {
  -ms-flex-direction: row;
  flex-direction: row;
}

html:not([dir="rtl"]) .nav-tabs-boxed.nav-tabs-boxed-right .nav-item {
  margin-left: -1px;
}

*[dir="rtl"] .nav-tabs-boxed.nav-tabs-boxed-right .nav-item {
  margin-right: -1px;
}

.nav-tabs-boxed.nav-tabs-boxed-right .nav-link {
  border-radius: 0 0.25rem 0.25rem 0;
}

.nav-tabs-boxed.nav-tabs-boxed-right .nav-link.active {
  border-color: #d8dbe0 #d8dbe0 #d8dbe0 #fff;
}

.nav-tabs-boxed.nav-tabs-boxed-right .tab-content {
  border-radius: 0.25rem 0 0.25rem 0.25rem;
}

.nav-pills .nav-link {
  border-radius: 0.25rem;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #321fdb;
}

.nav-underline {
  border-bottom: 2px solid;
  border-color: #c4c9d0;
}

.nav-underline .nav-item {
  margin-bottom: -2px;
}

.nav-underline .nav-link {
  border: 0;
  border-bottom: 2px solid transparent;
}

.nav-underline .nav-link.active,
.nav-underline .show > .nav-link {
  background: transparent;
}

.nav-underline.nav-underline-primary .nav-link.active,
.nav-underline.nav-underline-primary .show > .nav-link {
  color: #321fdb;
  border-color: #321fdb;
}

.nav-underline.nav-underline-secondary .nav-link.active,
.nav-underline.nav-underline-secondary .show > .nav-link {
  color: #ced2d8;
  border-color: #ced2d8;
}

.nav-underline.nav-underline-success .nav-link.active,
.nav-underline.nav-underline-success .show > .nav-link {
  color: #2eb85c;
  border-color: #2eb85c;
}

.nav-underline.nav-underline-info .nav-link.active,
.nav-underline.nav-underline-info .show > .nav-link {
  color: #39f;
  border-color: #39f;
}

.nav-underline.nav-underline-warning .nav-link.active,
.nav-underline.nav-underline-warning .show > .nav-link {
  color: #f9b115;
  border-color: #f9b115;
}

.nav-underline.nav-underline-danger .nav-link.active,
.nav-underline.nav-underline-danger .show > .nav-link {
  color: #e55353;
  border-color: #e55353;
}

.nav-underline.nav-underline-light .nav-link.active,
.nav-underline.nav-underline-light .show > .nav-link {
  color: #ebedef;
  border-color: #ebedef;
}

.nav-underline.nav-underline-dark .nav-link.active,
.nav-underline.nav-underline-dark .show > .nav-link {
  color: #636f83;
  border-color: #636f83;
}

.nav-fill .nav-item {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified .nav-item {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active {
  display: block;
}

.c-sidebar .nav-tabs:first-child .nav-link,
.c-sidebar .c-sidebar-close + .nav-tabs .nav-link {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  height: 56px;
  padding-top: 0;
  padding-bottom: 0;
}

.navbar {
  position: relative;
  padding: 0.5rem 1rem;
}

.navbar,.navbar .container,
.navbar .container-fluid, .navbar .container-sm, .navbar .container-md, .navbar .container-lg, .navbar .container-xl {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.navbar-brand {
  display: inline-block;
  padding-top: 0.3359375rem;
  padding-bottom: 0.3359375rem;
  margin-right: 1rem;
  font-size: 1.09375rem;
  line-height: inherit;
  white-space: nowrap;
}

.navbar-brand:hover, .navbar-brand:focus {
  text-decoration: none;
}

.navbar-nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-bottom: 0;
  list-style: none;
}

html:not([dir="rtl"]) .navbar-nav {
  padding-left: 0;
}

*[dir="rtl"] .navbar-nav {
  padding-right: 0;
}

.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}

.navbar-nav .dropdown-menu {
  position: static;
  float: none;
}

.navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.navbar-collapse {
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-align: center;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.09375rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.navbar-toggler:hover, .navbar-toggler:focus {
  text-decoration: none;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: no-repeat center center;
  background-size: 100% 100%;
}

@media (max-width: 575.98px) {
  .navbar-expand-sm > .container,
  .navbar-expand-sm > .container-fluid, .navbar-expand-sm > .container-sm, .navbar-expand-sm > .container-md, .navbar-expand-sm > .container-lg, .navbar-expand-sm > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm > .container,
  .navbar-expand-sm > .container-fluid, .navbar-expand-sm > .container-sm, .navbar-expand-sm > .container-md, .navbar-expand-sm > .container-lg, .navbar-expand-sm > .container-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .navbar-expand-sm .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}

@media (max-width: 767.98px) {
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid, .navbar-expand-md > .container-sm, .navbar-expand-md > .container-md, .navbar-expand-md > .container-lg, .navbar-expand-md > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 768px) {
  .navbar-expand-md {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md > .container,
  .navbar-expand-md > .container-fluid, .navbar-expand-md > .container-sm, .navbar-expand-md > .container-md, .navbar-expand-md > .container-lg, .navbar-expand-md > .container-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .navbar-expand-md .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}

@media (max-width: 991.98px) {
  .navbar-expand-lg > .container,
  .navbar-expand-lg > .container-fluid, .navbar-expand-lg > .container-sm, .navbar-expand-lg > .container-md, .navbar-expand-lg > .container-lg, .navbar-expand-lg > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 992px) {
  .navbar-expand-lg {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg > .container,
  .navbar-expand-lg > .container-fluid, .navbar-expand-lg > .container-sm, .navbar-expand-lg > .container-md, .navbar-expand-lg > .container-lg, .navbar-expand-lg > .container-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .navbar-expand-lg .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}

@media (max-width: 1199.98px) {
  .navbar-expand-xl > .container,
  .navbar-expand-xl > .container-fluid, .navbar-expand-xl > .container-sm, .navbar-expand-xl > .container-md, .navbar-expand-xl > .container-lg, .navbar-expand-xl > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 1200px) {
  .navbar-expand-xl {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl > .container,
  .navbar-expand-xl > .container-fluid, .navbar-expand-xl > .container-sm, .navbar-expand-xl > .container-md, .navbar-expand-xl > .container-lg, .navbar-expand-xl > .container-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .navbar-expand-xl .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}

.navbar-expand {
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.navbar-expand > .container,
.navbar-expand > .container-fluid, .navbar-expand > .container-sm, .navbar-expand > .container-md, .navbar-expand > .container-lg, .navbar-expand > .container-xl {
  padding-right: 0;
  padding-left: 0;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.navbar-expand .navbar-nav {
  -ms-flex-direction: row;
  flex-direction: row;
}

.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}

.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

.navbar-expand .navbar-collapse {
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}

.navbar-expand .navbar-toggler {
  display: none;
}

.navbar.navbar-dark .navbar-brand,.navbar.navbar-dark .navbar-brand:hover, .navbar.navbar-dark .navbar-brand:focus {
  color: #fff;
}

.navbar.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}

.navbar.navbar-dark .navbar-nav .nav-link:hover, .navbar.navbar-dark .navbar-nav .nav-link:focus {
  color: rgba(255, 255, 255, 0.75);
}

.navbar.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}

.navbar.navbar-dark .navbar-nav .show > .nav-link,
.navbar.navbar-dark .navbar-nav .active > .nav-link,
.navbar.navbar-dark .navbar-nav .nav-link.show,
.navbar.navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}

.navbar.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

.navbar.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.5);
}

.navbar.navbar-dark .navbar-text a,.navbar.navbar-dark .navbar-text a:hover, .navbar.navbar-dark .navbar-text a:focus {
  color: #fff;
}

.navbar.navbar-light .navbar-brand,.navbar.navbar-light .navbar-brand:hover, .navbar.navbar-light .navbar-brand:focus {
  color: rgba(0, 0, 21, 0.9);
}

.navbar.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 21, 0.5);
}

.navbar.navbar-light .navbar-nav .nav-link:hover, .navbar.navbar-light .navbar-nav .nav-link:focus {
  color: rgba(0, 0, 21, 0.7);
}

.navbar.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 21, 0.3);
}

.navbar.navbar-light .navbar-nav .show > .nav-link,
.navbar.navbar-light .navbar-nav .active > .nav-link,
.navbar.navbar-light .navbar-nav .nav-link.show,
.navbar.navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 21, 0.9);
}

.navbar.navbar-light .navbar-toggler {
  color: rgba(0, 0, 21, 0.5);
  border-color: rgba(0, 0, 21, 0.1);
}

.navbar.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(0, 0, 21, 0.5)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar.navbar-light .navbar-text {
  color: rgba(0, 0, 21, 0.5);
}

.navbar.navbar-light .navbar-text a,.navbar.navbar-light .navbar-text a:hover, .navbar.navbar-light .navbar-text a:focus {
  color: rgba(0, 0, 21, 0.9);
}

.pagination {
  display: -ms-flexbox;
  display: flex;
  list-style: none;
  border-radius: 0.25rem;
}

html:not([dir="rtl"]) .pagination {
  padding-left: 0;
}

*[dir="rtl"] .pagination {
  padding-right: 0;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  line-height: 1.25;
  border: 1px solid;
  color: #321fdb;
  background-color: #fff;
  border-color: #d8dbe0;
}

html:not([dir="rtl"]) .page-link {
  margin-left: -1px;
}

*[dir="rtl"] .page-link {
  margin-right: -1px;
}

.page-link:hover {
  z-index: 2;
  text-decoration: none;
  color: #231698;
  background-color: #d8dbe0;
  border-color: #c4c9d0;
}

.page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

html:not([dir="rtl"]) .page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

*[dir="rtl"] .page-item:first-child .page-link {
  margin-right: 0;
}

*[dir="rtl"] .page-item:first-child .page-link,html:not([dir="rtl"]) .page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

*[dir="rtl"] .page-item:last-child .page-link {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #321fdb;
  border-color: #321fdb;
}

.page-item.disabled .page-link {
  pointer-events: none;
  cursor: auto;
  color: #8a93a2;
  background-color: #fff;
  border-color: #c4c9d0;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.09375rem;
  line-height: 1.5;
}

html:not([dir="rtl"]) .pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}

*[dir="rtl"] .pagination-lg .page-item:first-child .page-link,html:not([dir="rtl"]) .pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

*[dir="rtl"] .pagination-lg .page-item:last-child .page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.765625rem;
  line-height: 1.5;
}

html:not([dir="rtl"]) .pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}

*[dir="rtl"] .pagination-sm .page-item:first-child .page-link,html:not([dir="rtl"]) .pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}

*[dir="rtl"] .pagination-sm .page-item:last-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}

.popover {
  top: 0;
  left: 0;
  z-index: 1060;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.765625rem;
  word-wrap: break-word;
  background-clip: padding-box;
  border: 1px solid;
  border-radius: 0.3rem;
  background-color: #fff;
  border-color: rgba(0, 0, 21, 0.2);
}

.popover,.popover .arrow {
  position: absolute;
  display: block;
}

.popover .arrow {
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}

.popover .arrow::before, .popover .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-popover-top, .bs-popover-auto[x-placement^="top"] {
  margin-bottom: 0.5rem;
}

.bs-popover-top > .arrow, .bs-popover-auto[x-placement^="top"] > .arrow {
  bottom: calc(-0.5rem - 1px);
}

.bs-popover-top > .arrow::before, .bs-popover-auto[x-placement^="top"] > .arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 21, 0.25);
}

.bs-popover-top > .arrow::after, .bs-popover-auto[x-placement^="top"] > .arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}

.bs-popover-right, .bs-popover-auto[x-placement^="right"] {
  margin-left: 0.5rem;
}

.bs-popover-right > .arrow, .bs-popover-auto[x-placement^="right"] > .arrow {
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.bs-popover-right > .arrow::before, .bs-popover-auto[x-placement^="right"] > .arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 21, 0.25);
}

.bs-popover-right > .arrow::after, .bs-popover-auto[x-placement^="right"] > .arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}

.bs-popover-bottom, .bs-popover-auto[x-placement^="bottom"] {
  margin-top: 0.5rem;
}

.bs-popover-bottom > .arrow, .bs-popover-auto[x-placement^="bottom"] > .arrow {
  top: calc(-0.5rem - 1px);
}

.bs-popover-bottom > .arrow::before, .bs-popover-auto[x-placement^="bottom"] > .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 21, 0.25);
}

.bs-popover-bottom > .arrow::after, .bs-popover-auto[x-placement^="bottom"] > .arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}

.bs-popover-bottom .popover-header::before, .bs-popover-auto[x-placement^="bottom"] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid;
  border-bottom-color: #f7f7f7;
}

.bs-popover-left, .bs-popover-auto[x-placement^="left"] {
  margin-right: 0.5rem;
}

.bs-popover-left > .arrow, .bs-popover-auto[x-placement^="left"] > .arrow {
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.bs-popover-left > .arrow::before, .bs-popover-auto[x-placement^="left"] > .arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 21, 0.25);
}

.bs-popover-left > .arrow::after, .bs-popover-auto[x-placement^="left"] > .arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}

.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  border-bottom: 1px solid;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
  background-color: #f7f7f7;
  border-bottom-color: #ebebeb;
}

.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 0.5rem 0.75rem;
  color: #4f5d73;
}

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

.progress {
  height: 1rem;
  font-size: 0.65625rem;
  border-radius: 0.25rem;
  background-color: #ebedef;
}

.progress,.progress-bar {
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
}

.progress-bar {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
  transition: width 0.6s ease;
  color: #fff;
  background-color: #321fdb;
}

@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  -webkit-animation: progress-bar-stripes 1s linear infinite;
  animation: progress-bar-stripes 1s linear infinite;
}

@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    -webkit-animation: none;
    animation: none;
  }
}

.progress-xs {
  height: 4px;
}

.progress-sm {
  height: 8px;
}

.progress.progress-white {
  background-color: rgba(255, 255, 255, 0.2);
}

.progress.progress-white .progress-bar {
  background-color: #fff;
}

.progress-group {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  margin-bottom: 1rem;
}

.progress-group-prepend {
  -ms-flex: 0 0 100px;
  flex: 0 0 100px;
  -ms-flex-item-align: center;
  align-self: center;
}

.progress-group-icon {
  font-size: 1.09375rem;
}

html:not([dir="rtl"]) .progress-group-icon {
  margin: 0 1rem 0 0.25rem;
}

*[dir="rtl"] .progress-group-icon {
  margin: 0 0.25rem 0 1rem;
}

.progress-group-text {
  font-size: 0.765625rem;
  color: #768192;
}

.progress-group-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -ms-flex-align: end;
  align-items: flex-end;
  margin-bottom: 0.25rem;
}

.progress-group-bars {
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-item-align: center;
  align-self: center;
}

.progress-group-bars .progress:not(:last-child) {
  margin-bottom: 2px;
}

.progress-group-header + .progress-group-bars {
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
}

.c-sidebar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 0 0 256px;
  flex: 0 0 256px;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-order: -1;
  order: -1;
  width: 256px;
  padding: 0;
  box-shadow: none;
  color: #fff;
  background: #3c4b64;
  will-change: auto;
  transition: box-shadow 0.25s, margin-left 0.25s, margin-right 0.25s, width 0.25s, z-index 0s ease 0.25s, -webkit-transform 0.25s;
  transition: box-shadow 0.25s, transform 0.25s, margin-left 0.25s, margin-right 0.25s, width 0.25s, z-index 0s ease 0.25s;
  transition: box-shadow 0.25s, transform 0.25s, margin-left 0.25s, margin-right 0.25s, width 0.25s, z-index 0s ease 0.25s, -webkit-transform 0.25s;
}

@media (max-width: 991.98px) {
  .c-sidebar {
    --is-mobile: true;
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 1031;
  }
}

html:not([dir="rtl"]) .c-sidebar:not(.c-sidebar-right) {
  margin-left: -256px;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-right {
  -ms-flex-order: 99;
  order: 99;
  margin-right: -256px;
}

*[dir="rtl"] .c-sidebar:not(.c-sidebar-right) {
  margin-right: -256px;
}

*[dir="rtl"] .c-sidebar.c-sidebar-right {
  margin-left: -256px;
  border: 0;
}

.c-sidebar[class*="bg-"] {
  border-color: rgba(0, 0, 21, 0.1);
}

.c-sidebar.c-sidebar-sm {
  width: 192px;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm:not(.c-sidebar-right) {
  margin-left: -192px;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm.c-sidebar-right,*[dir="rtl"] .c-sidebar.c-sidebar-sm:not(.c-sidebar-right) {
  margin-right: -192px;
}

*[dir="rtl"] .c-sidebar.c-sidebar-sm.c-sidebar-right {
  margin-left: -192px;
}

.c-sidebar.c-sidebar-lg {
  width: 320px;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg:not(.c-sidebar-right) {
  margin-left: -320px;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg.c-sidebar-right,*[dir="rtl"] .c-sidebar.c-sidebar-lg:not(.c-sidebar-right) {
  margin-right: -320px;
}

*[dir="rtl"] .c-sidebar.c-sidebar-lg.c-sidebar-right {
  margin-left: -320px;
}

.c-sidebar.c-sidebar-xl {
  width: 384px;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl:not(.c-sidebar-right) {
  margin-left: -384px;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl.c-sidebar-right,*[dir="rtl"] .c-sidebar.c-sidebar-xl:not(.c-sidebar-right) {
  margin-right: -384px;
}

*[dir="rtl"] .c-sidebar.c-sidebar-xl.c-sidebar-right {
  margin-left: -384px;
}

@media (min-width: 992px) {
  .c-sidebar.c-sidebar-fixed {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 1030;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-fixed:not(.c-sidebar-right) {
    left: 0;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-fixed.c-sidebar-right,*[dir="rtl"] .c-sidebar.c-sidebar-fixed:not(.c-sidebar-right) {
    right: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-fixed.c-sidebar-right {
    left: 0;
  }
}

.c-sidebar.c-sidebar-overlaid {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 1032;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-overlaid:not(.c-sidebar-right) {
  left: 0;
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-overlaid.c-sidebar-right,*[dir="rtl"] .c-sidebar.c-sidebar-overlaid:not(.c-sidebar-right) {
  right: 0;
}

*[dir="rtl"] .c-sidebar.c-sidebar-overlaid.c-sidebar-right {
  left: 0;
}

.c-sidebar-close {
  position: absolute;
  width: 56px;
  height: 56px;
  background: transparent;
  border: 0;
}

html:not([dir="rtl"]) .c-sidebar-close {
  right: 0;
}

*[dir="rtl"] .c-sidebar-close {
  left: 0;
}

.c-sidebar-brand {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 0 0 56px;
  flex: 0 0 56px;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c-sidebar-brand .c-sidebar-brand-minimized {
  display: none;
}

.c-sidebar-header {
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  padding: 0.75rem 1rem;
  text-align: center;
  transition: 0.25s;
}

.c-sidebar-nav {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 1;
  flex: 1;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 0;
  margin-bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
  list-style: none;
}

.c-sidebar-nav-title {
  padding: 0.75rem 1rem;
  margin-top: 1rem;
  font-size: 80%;
  font-weight: 700;
  text-transform: uppercase;
  transition: 0.25s;
}

.c-sidebar-nav-divider {
  height: 10px;
  transition: height 0.25s;
}

.c-sidebar-nav-item {
  width: inherit;
}

.c-sidebar-nav-link, .c-sidebar-nav-dropdown-toggle {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 1;
  flex: 1;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.8445rem 1rem;
  text-decoration: none;
  white-space: nowrap;
  transition: background 0.25s, color 0.25s;
}

html:not([dir="rtl"]) .c-sidebar-nav-link .badge, html:not([dir="rtl"]) .c-sidebar-nav-dropdown-toggle .badge {
  margin-left: auto;
}

*[dir="rtl"] .c-sidebar-nav-link .badge, *[dir="rtl"] .c-sidebar-nav-dropdown-toggle .badge {
  margin-right: auto;
}

.c-sidebar-nav-link.c-disabled, .c-disabled.c-sidebar-nav-dropdown-toggle {
  cursor: default;
}

.c-sidebar-nav-link:hover, .c-sidebar-nav-dropdown-toggle:hover {
  text-decoration: none;
}

.c-sidebar-nav-icon {
  -ms-flex: 0 0 56px;
  flex: 0 0 56px;
  height: 1.09375rem;
  font-size: 1.09375rem;
  text-align: center;
  transition: 0.25s;
  fill: currentColor;
}

html:not([dir="rtl"]) .c-sidebar-nav-icon:first-child {
  margin-left: -1rem;
}

*[dir="rtl"] .c-sidebar-nav-icon:first-child {
  margin-right: -1rem;
}

.c-sidebar-nav-dropdown {
  position: relative;
  transition: background 0.25s ease-in-out;
}

.c-sidebar-nav-dropdown.c-show > .c-sidebar-nav-dropdown-items {
  max-height: 1500px;
}

html:not([dir="rtl"]) .c-sidebar-nav-dropdown.c-show > .c-sidebar-nav-dropdown-toggle::after {
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

*[dir="rtl"] .c-sidebar-nav-dropdown.c-show > .c-sidebar-nav-dropdown-toggle::after {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}

.c-sidebar-nav-dropdown.c-show + .c-sidebar-nav-dropdown.c-show {
  margin-top: 1px;
}

.c-sidebar-nav-dropdown-toggle {
  cursor: pointer;
}

.c-sidebar-nav-dropdown-toggle::after {
  display: block;
  -ms-flex: 0 8px;
  flex: 0 8px;
  height: 8px;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  transition: -webkit-transform 0.25s;
  transition: transform 0.25s;
  transition: transform 0.25s, -webkit-transform 0.25s;
}

html:not([dir="rtl"]) .c-sidebar-nav-dropdown-toggle::after {
  margin-left: auto;
}

*[dir="rtl"] .c-sidebar-nav-dropdown-toggle::after {
  margin-right: auto;
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

html:not([dir="rtl"]) .c-sidebar-nav-dropdown-toggle .badge {
  margin-right: 1rem;
}

*[dir="rtl"] .c-sidebar-nav-dropdown-toggle .badge {
  margin-left: 1rem;
}

.c-sidebar-nav-dropdown-items {
  max-height: 0;
  padding: 0;
  overflow-y: hidden;
  transition: max-height 0.25s ease-in-out;
}

html:not([dir="rtl"]) .c-sidebar-nav-dropdown-items .c-sidebar-nav-link, html:not([dir="rtl"]) .c-sidebar-nav-dropdown-items .c-sidebar-nav-dropdown-toggle {
  padding-left: 56px;
}

*[dir="rtl"] .c-sidebar-nav-dropdown-items .c-sidebar-nav-link, *[dir="rtl"] .c-sidebar-nav-dropdown-items .c-sidebar-nav-dropdown-toggle {
  padding-right: 56px;
}

html:not([dir="rtl"]) .c-sidebar-nav-dropdown-items .c-sidebar-nav-link .c-sidebar-nav-icon, html:not([dir="rtl"]) .c-sidebar-nav-dropdown-items .c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  margin-left: -56px;
}

*[dir="rtl"] .c-sidebar-nav-dropdown-items .c-sidebar-nav-link .c-sidebar-nav-icon, *[dir="rtl"] .c-sidebar-nav-dropdown-items .c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  margin-right: -56px;
}

.c-sidebar-nav-label {
  display: -ms-flexbox;
  display: flex;
  padding: 0.211125rem 1rem;
  transition: 0.25s;
}

.c-sidebar-nav-label:hover {
  text-decoration: none;
}

.c-sidebar-nav-label .c-sidebar-nav-icon {
  margin-top: 1px;
}

.c-sidebar-footer {
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  padding: 0.75rem 1rem;
  transition: 0.25s;
}

.c-sidebar-minimizer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 0 0 50px;
  flex: 0 0 50px;
  -ms-flex-pack: end;
  justify-content: flex-end;
  width: inherit;
  padding: 0;
  cursor: pointer;
  border: 0;
}

@media (max-width: 991.98px) {
  .c-sidebar-minimizer {
    display: none;
  }
}

.c-sidebar-minimizer::before {
  display: block;
  width: 50px;
  height: 50px;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  background-size: 12.5px;
  transition: 0.25s;
}

*[dir="rtl"] .c-sidebar-minimizer::before {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.c-sidebar-minimizer:focus, .c-sidebar-minimizer.c-focus {
  outline: 0;
}

.c-sidebar-right .c-sidebar-minimizer {
  -ms-flex-pack: start;
  justify-content: flex-start;
}

html:not([dir="rtl"]) .c-sidebar-right .c-sidebar-minimizer::before {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

*[dir="rtl"] .c-sidebar-right .c-sidebar-minimizer::before {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
}

@media (max-width: 991.98px) {
  .c-sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1030;
    width: 100vw;
    height: 100vh;
    background-color: #000015;
    transition: 0.25s;
  }
  .c-sidebar-backdrop.c-fade {
    opacity: 0;
  }
  .c-sidebar-backdrop.c-show {
    opacity: 0.5;
  }
}

@media (min-width: 992px) {
  .c-sidebar-minimized {
    z-index: 1031;
    -ms-flex: 0 0 56px;
    flex: 0 0 56px;
  }
  .c-sidebar-minimized.c-sidebar-fixed {
    z-index: 1031;
    width: 56px;
  }
  html:not([dir="rtl"]) .c-sidebar-minimized:not(.c-sidebar-right) {
    margin-left: -56px;
  }
  *[dir="rtl"] .c-sidebar-minimized:not(.c-sidebar-right) {
    margin-right: -56px;
  }
  html:not([dir="rtl"]) .c-sidebar-minimized.c-sidebar-right {
    margin-right: -56px;
    margin-left: -56px;
  }
  .c-sidebar-minimized .c-sidebar-brand-full {
    display: none;
  }
  .c-sidebar-minimized .c-sidebar-brand-minimized {
    display: block;
  }
  .c-sidebar-minimized .c-sidebar-nav {
    padding-bottom: 50px;
    overflow: visible;
  }
  .c-sidebar-minimized .c-d-minimized-none,
  .c-sidebar-minimized .c-sidebar-nav-divider,
  .c-sidebar-minimized .c-sidebar-nav-label,
  .c-sidebar-minimized .c-sidebar-nav-title,
  .c-sidebar-minimized .c-sidebar-footer,
  .c-sidebar-minimized .c-sidebar-form,
  .c-sidebar-minimized .c-sidebar-header {
    height: 0;
    padding: 0;
    margin: 0;
    visibility: hidden;
    opacity: 0;
  }
  .c-sidebar-minimized .c-sidebar-minimizer {
    position: fixed;
    bottom: 0;
    width: inherit;
  }
  html:not([dir="rtl"]) .c-sidebar-minimized .c-sidebar-minimizer::before {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
  }
  *[dir="rtl"] .c-sidebar-minimized .c-sidebar-minimizer::before,html:not([dir="rtl"]) .c-sidebar-minimized.c-sidebar-right .c-sidebar-minimizer::before {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  *[dir="rtl"] .c-sidebar-minimized.c-sidebar-right .c-sidebar-minimizer::before {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }
  html:not([dir="rtl"]) .c-sidebar-minimized.c-sidebar-right .c-sidebar-nav > .c-sidebar-nav-item:hover, html:not([dir="rtl"])
  .c-sidebar-minimized.c-sidebar-right .c-sidebar-nav > .c-sidebar-nav-dropdown:hover {
    margin-left: -256px;
  }
  *[dir="rtl"] .c-sidebar-minimized.c-sidebar-right .c-sidebar-nav > .c-sidebar-nav-item:hover, *[dir="rtl"]
  .c-sidebar-minimized.c-sidebar-right .c-sidebar-nav > .c-sidebar-nav-dropdown:hover {
    margin-right: -256px;
  }
  .c-sidebar-minimized .c-sidebar-nav-link, .c-sidebar-minimized .c-sidebar-nav-dropdown-toggle,
  .c-sidebar-minimized .c-sidebar-nav-dropdown-toggle {
    overflow: hidden;
    white-space: nowrap;
    border-left: 0;
  }
  .c-sidebar-minimized .c-sidebar-nav-link:hover, .c-sidebar-minimized .c-sidebar-nav-dropdown-toggle:hover,
  .c-sidebar-minimized .c-sidebar-nav-dropdown-toggle:hover {
    width: 312px;
  }
  .c-sidebar-minimized .c-sidebar-nav-dropdown-toggle::after {
    display: none;
  }
  .c-sidebar-minimized .c-sidebar-nav-dropdown-items .c-sidebar-nav-link, .c-sidebar-minimized .c-sidebar-nav-dropdown-items .c-sidebar-nav-dropdown-toggle {
    width: 256px;
  }
  .c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown {
    position: relative;
  }
  .c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown > .c-sidebar-nav-dropdown-items,.c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown > .c-sidebar-nav-dropdown-items .c-sidebar-nav-dropdown:not(.c-show) > .c-sidebar-nav-dropdown-items {
    display: none;
  }
  .c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown .c-sidebar-nav-dropdown-items {
    max-height: 1500px;
  }
  .c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown:hover {
    width: 312px;
    overflow: visible;
  }
  .c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown:hover > .c-sidebar-nav-dropdown-items {
    position: absolute;
    display: inline;
  }
  html:not([dir="rtl"]) .c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown:hover > .c-sidebar-nav-dropdown-items {
    left: 56px;
  }
  *[dir="rtl"] .c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown:hover > .c-sidebar-nav-dropdown-items {
    right: 56px;
  }
  html:not([dir="rtl"]) .c-sidebar-minimized.c-sidebar-right > .c-sidebar-nav-dropdown:hover > .c-sidebar-nav-dropdown-items {
    left: 0;
  }
  *[dir="rtl"] .c-sidebar-minimized.c-sidebar-right > .c-sidebar-nav-dropdown:hover > .c-sidebar-nav-dropdown-items {
    right: 0;
  }
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-show:not(.c-sidebar-right), html:not([dir="rtl"])
.c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
  margin-left: 0;
}

*[dir="rtl"] .c-sidebar.c-sidebar-show:not(.c-sidebar-right), *[dir="rtl"]
.c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
  margin-right: 0;
}

@media (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-show.c-sidebar-right, html:not([dir="rtl"])
.c-sidebar.c-sidebar-show.c-sidebar-right {
  margin-right: 0;
}

*[dir="rtl"] .c-sidebar.c-sidebar-show.c-sidebar-right, *[dir="rtl"]
.c-sidebar.c-sidebar-show.c-sidebar-right {
  margin-left: 0;
}

@media (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
}

@media (min-width: 576px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right), html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
    margin-left: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right), *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
    margin-right: 0;
  }
}

@media (min-width: 576px) and (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
}

@media (min-width: 576px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show.c-sidebar-right, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right {
    margin-right: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show.c-sidebar-right, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right {
    margin-left: 0;
  }
}

@media (min-width: 576px) and (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-sm-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
}

@media (min-width: 768px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right), html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
    margin-left: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right), *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
    margin-right: 0;
  }
}

@media (min-width: 768px) and (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
}

@media (min-width: 768px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show.c-sidebar-right, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right {
    margin-right: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show.c-sidebar-right, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right {
    margin-left: 0;
  }
}

@media (min-width: 768px) and (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-md-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
}

@media (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right), html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
    margin-left: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right), *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
    margin-right: 0;
  }
}

@media (min-width: 992px) and (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
}

@media (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show.c-sidebar-right, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right {
    margin-right: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show.c-sidebar-right, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right {
    margin-left: 0;
  }
}

@media (min-width: 992px) and (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-lg-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
}

@media (min-width: 1200px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right), html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
    margin-left: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right), *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right) {
    margin-right: 0;
  }
}

@media (min-width: 1200px) and (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show:not(.c-sidebar-right).c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
}

@media (min-width: 1200px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show.c-sidebar-right, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right {
    margin-right: 0;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show.c-sidebar-right, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right {
    margin-left: 0;
  }
}

@media (min-width: 1200px) and (min-width: 992px) {
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-right: 256px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed ~ .c-wrapper {
    margin-left: 256px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-right: 192px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-sm ~ .c-wrapper {
    margin-left: 192px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-right: 320px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-lg ~ .c-wrapper {
    margin-left: 320px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-right: 384px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-xl ~ .c-wrapper {
    margin-left: 384px;
  }
  html:not([dir="rtl"]) .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, html:not([dir="rtl"])
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-right: 56px;
  }
  *[dir="rtl"] .c-sidebar.c-sidebar-xl-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper, *[dir="rtl"]
  .c-sidebar.c-sidebar-show.c-sidebar-right.c-sidebar-fixed.c-sidebar-minimized ~ .c-wrapper {
    margin-left: 56px;
  }
}

.c-sidebar .c-sidebar-close,.c-sidebar .c-sidebar-brand {
  color: #fff;
}

.c-sidebar .c-sidebar-brand,.c-sidebar .c-sidebar-header {
  background: rgba(0, 0, 21, 0.2);
}

.c-sidebar .c-sidebar-form .c-form-control {
  color: #fff;
  background: rgba(0, 0, 21, 0.1);
  border: 0;
}

.c-sidebar .c-sidebar-form .c-form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-form .c-form-control::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-form .c-form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-form .c-form-control::-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-form .c-form-control::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-title {
  color: rgba(255, 255, 255, 0.6);
}

.c-sidebar .c-sidebar-nav-link, .c-sidebar .c-sidebar-nav-dropdown-toggle {
  color: rgba(255, 255, 255, 0.8);
  background: transparent;
}

.c-sidebar .c-sidebar-nav-link .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.5);
}

.c-sidebar .c-sidebar-nav-link.c-active, .c-sidebar .c-active.c-sidebar-nav-dropdown-toggle {
  color: #fff;
  background: rgba(255, 255, 255, 0.05);
}

.c-sidebar .c-sidebar-nav-link.c-active .c-sidebar-nav-icon, .c-sidebar .c-active.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link:hover, .c-sidebar .c-sidebar-nav-dropdown-toggle:hover {
  color: #fff;
  background: #321fdb;
}

.c-sidebar .c-sidebar-nav-link:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link:hover.c-sidebar-nav-dropdown-toggle::after, .c-sidebar :hover.c-sidebar-nav-dropdown-toggle::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23fff' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar .c-sidebar-nav-link.c-disabled, .c-sidebar .c-disabled.c-sidebar-nav-dropdown-toggle {
  color: #b3b2b2;
  background: transparent;
}

.c-sidebar .c-sidebar-nav-link.c-disabled .c-sidebar-nav-icon, .c-sidebar .c-disabled.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.5);
}

.c-sidebar .c-sidebar-nav-link.c-disabled:hover, .c-sidebar .c-disabled.c-sidebar-nav-dropdown-toggle:hover {
  color: #b3b2b2;
}

.c-sidebar .c-sidebar-nav-link.c-disabled:hover .c-sidebar-nav-icon, .c-sidebar .c-disabled.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.5);
}

.c-sidebar .c-sidebar-nav-link.c-disabled:hover.c-sidebar-nav-dropdown-toggle::after, .c-sidebar .c-disabled:hover.c-sidebar-nav-dropdown-toggle::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23fff' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar .c-sidebar-nav-dropdown-toggle {
  position: relative;
}

.c-sidebar .c-sidebar-nav-dropdown-toggle::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='rgba(255, 255, 255, 0.5)' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar .c-sidebar-nav-dropdown.c-show {
  background: rgba(0, 0, 0, 0.2);
}

.c-sidebar .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-link, .c-sidebar .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-dropdown-toggle {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-link.c-disabled, .c-sidebar .c-sidebar-nav-dropdown.c-show .c-disabled.c-sidebar-nav-dropdown-toggle {
  color: #b3b2b2;
  background: transparent;
}

.c-sidebar .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-link.c-disabled:hover, .c-sidebar .c-sidebar-nav-dropdown.c-show .c-disabled.c-sidebar-nav-dropdown-toggle:hover {
  color: #b3b2b2;
}

.c-sidebar .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-link.c-disabled:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-dropdown.c-show .c-disabled.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.5);
}

.c-sidebar .c-sidebar-nav-label {
  color: rgba(255, 255, 255, 0.6);
}

.c-sidebar .c-sidebar-nav-label:hover {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-label .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.5);
}

.c-sidebar .c-progress {
  background-color: #596f94 !important;
}

.c-sidebar .c-sidebar-footer {
  background: rgba(0, 0, 21, 0.2);
}

.c-sidebar .c-sidebar-minimizer {
  background-color: rgba(0, 0, 21, 0.2);
}

.c-sidebar .c-sidebar-minimizer::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%238a93a2' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar .c-sidebar-minimizer:focus, .c-sidebar .c-sidebar-minimizer.c-focus {
  outline: 0;
}

.c-sidebar .c-sidebar-minimizer:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.c-sidebar .c-sidebar-minimizer:hover::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23fff' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-link, .c-sidebar.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-dropdown-toggle {
  background: #321fdb;
}

.c-sidebar.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-link .c-sidebar-nav-icon, .c-sidebar.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-link.c-disabled, .c-sidebar.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-disabled.c-sidebar-nav-dropdown-toggle {
  background: #3c4b64;
}

.c-sidebar.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-link.c-disabled .c-sidebar-nav-icon, .c-sidebar.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-disabled.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.5);
}

.c-sidebar.c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown > .c-sidebar-nav-dropdown-items {
  background: #3c4b64;
}

.c-sidebar.c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown:hover {
  background: #321fdb;
}

.c-sidebar.c-sidebar-light {
  color: #4f5d73;
  background: #fff;
  border-right: 1px solid rgba(159, 167, 179, 0.5);
}

html:not([dir="rtl"]) .c-sidebar.c-sidebar-light.c-sidebar-right,*[dir="rtl"] .c-sidebar.c-sidebar-light {
  border-right: 0;
  border-left: 1px solid rgba(159, 167, 179, 0.5);
}

*[dir="rtl"] .c-sidebar.c-sidebar-light.c-sidebar-right {
  border: 0;
  border-right: 1px solid rgba(159, 167, 179, 0.5);
}

.c-sidebar.c-sidebar-light .c-sidebar-close {
  color: #4f5d73;
}

.c-sidebar.c-sidebar-light .c-sidebar-brand {
  color: #fff;
  background: #321fdb;
}

.c-sidebar.c-sidebar-light .c-sidebar-header {
  background: rgba(0, 0, 21, 0.2);
}

.c-sidebar.c-sidebar-light .c-sidebar-form .c-form-control {
  color: #fff;
  background: rgba(0, 0, 21, 0.1);
  border: 0;
}

.c-sidebar.c-sidebar-light .c-sidebar-form .c-form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar.c-sidebar-light .c-sidebar-form .c-form-control::-moz-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar.c-sidebar-light .c-sidebar-form .c-form-control:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar.c-sidebar-light .c-sidebar-form .c-form-control::-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar.c-sidebar-light .c-sidebar-form .c-form-control::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-title {
  color: rgba(0, 0, 21, 0.4);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link, .c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown-toggle {
  color: rgba(0, 0, 21, 0.8);
  background: transparent;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link .c-sidebar-nav-icon, .c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(0, 0, 21, 0.5);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link.c-active, .c-sidebar.c-sidebar-light .c-active.c-sidebar-nav-dropdown-toggle {
  color: rgba(0, 0, 21, 0.8);
  background: rgba(0, 0, 21, 0.05);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link.c-active .c-sidebar-nav-icon, .c-sidebar.c-sidebar-light .c-active.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: #321fdb;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link:hover, .c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown-toggle:hover {
  color: #fff;
  background: #321fdb;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link:hover .c-sidebar-nav-icon, .c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link:hover.c-sidebar-nav-dropdown-toggle::after, .c-sidebar.c-sidebar-light :hover.c-sidebar-nav-dropdown-toggle::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23fff' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link.c-disabled, .c-sidebar.c-sidebar-light .c-disabled.c-sidebar-nav-dropdown-toggle {
  color: #b3b2b2;
  background: transparent;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link.c-disabled .c-sidebar-nav-icon, .c-sidebar.c-sidebar-light .c-disabled.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(0, 0, 21, 0.5);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link.c-disabled:hover, .c-sidebar.c-sidebar-light .c-disabled.c-sidebar-nav-dropdown-toggle:hover {
  color: #b3b2b2;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link.c-disabled:hover .c-sidebar-nav-icon, .c-sidebar.c-sidebar-light .c-disabled.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: rgba(0, 0, 21, 0.5);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-link.c-disabled:hover.c-sidebar-nav-dropdown-toggle::after, .c-sidebar.c-sidebar-light .c-disabled:hover.c-sidebar-nav-dropdown-toggle::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23fff' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown-toggle {
  position: relative;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown-toggle::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='rgba(0, 0, 21, 0.5)' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show {
  background: rgba(0, 0, 0, 0.05);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-link, .c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-dropdown-toggle {
  color: rgba(0, 0, 21, 0.8);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-link.c-disabled, .c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show .c-disabled.c-sidebar-nav-dropdown-toggle {
  color: #b3b2b2;
  background: transparent;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-link.c-disabled:hover, .c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show .c-disabled.c-sidebar-nav-dropdown-toggle:hover {
  color: #b3b2b2;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show .c-sidebar-nav-link.c-disabled:hover .c-sidebar-nav-icon, .c-sidebar.c-sidebar-light .c-sidebar-nav-dropdown.c-show .c-disabled.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: rgba(0, 0, 21, 0.5);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-label {
  color: rgba(0, 0, 21, 0.4);
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-label:hover {
  color: #4f5d73;
}

.c-sidebar.c-sidebar-light .c-sidebar-nav-label .c-sidebar-nav-icon {
  color: rgba(0, 0, 21, 0.5);
}

.c-sidebar.c-sidebar-light .c-sidebar-footer {
  background: rgba(0, 0, 21, 0.2);
}

.c-sidebar.c-sidebar-light .c-sidebar-minimizer {
  background-color: rgba(0, 0, 0, 0.05);
}

.c-sidebar.c-sidebar-light .c-sidebar-minimizer::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%238a93a2' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar.c-sidebar-light .c-sidebar-minimizer:focus, .c-sidebar.c-sidebar-light .c-sidebar-minimizer.c-focus {
  outline: 0;
}

.c-sidebar.c-sidebar-light .c-sidebar-minimizer:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.c-sidebar.c-sidebar-light .c-sidebar-minimizer:hover::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%23768192' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E");
}

.c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-link, .c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-dropdown-toggle {
  background: #321fdb;
}

.c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-link .c-sidebar-nav-icon, .c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-link.c-disabled, .c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-disabled.c-sidebar-nav-dropdown-toggle {
  background: #fff;
}

.c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-sidebar-nav-link.c-disabled .c-sidebar-nav-icon, .c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav-item:hover > .c-disabled.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(0, 0, 21, 0.5);
}

.c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown > .c-sidebar-nav-dropdown-items {
  background: #fff;
}

.c-sidebar.c-sidebar-light.c-sidebar-minimized .c-sidebar-nav > .c-sidebar-nav-dropdown:hover,.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-primary, .c-sidebar .c-sidebar-nav-link-primary.c-sidebar-nav-dropdown-toggle {
  background: #321fdb;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-primary .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-primary.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-primary:hover, .c-sidebar .c-sidebar-nav-link-primary.c-sidebar-nav-dropdown-toggle:hover {
  background: #2d1cc5;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-primary:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-primary.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-secondary, .c-sidebar .c-sidebar-nav-link-secondary.c-sidebar-nav-dropdown-toggle {
  background: #ced2d8;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-secondary .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-secondary.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-secondary:hover, .c-sidebar .c-sidebar-nav-link-secondary.c-sidebar-nav-dropdown-toggle:hover {
  background: #c0c5cd;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-secondary:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-secondary.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-success, .c-sidebar .c-sidebar-nav-link-success.c-sidebar-nav-dropdown-toggle {
  background: #2eb85c;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-success .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-success.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-success:hover, .c-sidebar .c-sidebar-nav-link-success.c-sidebar-nav-dropdown-toggle:hover {
  background: #29a452;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-success:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-success.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-info, .c-sidebar .c-sidebar-nav-link-info.c-sidebar-nav-dropdown-toggle {
  background: #39f;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-info .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-info.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-info:hover, .c-sidebar .c-sidebar-nav-link-info.c-sidebar-nav-dropdown-toggle:hover {
  background: #1a8cff;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-info:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-info.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-warning, .c-sidebar .c-sidebar-nav-link-warning.c-sidebar-nav-dropdown-toggle {
  background: #f9b115;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-warning .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-warning.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-warning:hover, .c-sidebar .c-sidebar-nav-link-warning.c-sidebar-nav-dropdown-toggle:hover {
  background: #eea506;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-warning:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-warning.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-danger, .c-sidebar .c-sidebar-nav-link-danger.c-sidebar-nav-dropdown-toggle {
  background: #e55353;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-danger .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-danger.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-danger:hover, .c-sidebar .c-sidebar-nav-link-danger.c-sidebar-nav-dropdown-toggle:hover {
  background: #e23d3d;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-danger:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-danger.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-light, .c-sidebar .c-sidebar-nav-link-light.c-sidebar-nav-dropdown-toggle {
  background: #ebedef;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-light .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-light.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-light:hover, .c-sidebar .c-sidebar-nav-link-light.c-sidebar-nav-dropdown-toggle:hover {
  background: #dde0e4;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-light:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-light.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-dark, .c-sidebar .c-sidebar-nav-link-dark.c-sidebar-nav-dropdown-toggle {
  background: #636f83;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-dark .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-dark.c-sidebar-nav-dropdown-toggle .c-sidebar-nav-icon {
  color: rgba(255, 255, 255, 0.7);
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-dark:hover, .c-sidebar .c-sidebar-nav-link-dark.c-sidebar-nav-dropdown-toggle:hover {
  background: #586374;
}

.c-sidebar .c-sidebar-nav-link.c-sidebar-nav-link-dark:hover .c-sidebar-nav-icon, .c-sidebar .c-sidebar-nav-link-dark.c-sidebar-nav-dropdown-toggle:hover .c-sidebar-nav-icon {
  color: #fff;
}

@-webkit-keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border .75s linear infinite;
  animation: spinner-border .75s linear infinite;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

@-webkit-keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}

@keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}

.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: spinner-grow .75s linear infinite;
  animation: spinner-grow .75s linear infinite;
}

.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

.c-subheader {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  min-height: 48px;
  background: #fff;
  border-bottom: 1px solid #d8dbe0;
}

.c-subheader[class*="bg-"] {
  border-color: rgba(0, 0, 21, 0.1);
}

.c-subheader.c-subheader-fixed {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}

.c-subheader-nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-align: center;
  align-items: center;
  min-height: 48px;
  padding: 0;
  margin-bottom: 0;
  list-style: none;
}

.c-subheader-nav .c-subheader-nav-item {
  position: relative;
}

.c-subheader-nav .c-subheader-nav-btn {
  background-color: transparent;
  border: 1px solid transparent;
}

.c-subheader-nav .c-subheader-nav-link,
.c-subheader-nav .c-subheader-nav-btn {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

.c-subheader-nav .c-subheader-nav-link .badge,
.c-subheader-nav .c-subheader-nav-btn .badge {
  position: absolute;
  top: 50%;
  margin-top: -16px;
}

html:not([dir="rtl"]) .c-subheader-nav .c-subheader-nav-link .badge, html:not([dir="rtl"])
.c-subheader-nav .c-subheader-nav-btn .badge {
  left: 50%;
  margin-left: 0;
}

*[dir="rtl"] .c-subheader-nav .c-subheader-nav-link .badge, *[dir="rtl"]
.c-subheader-nav .c-subheader-nav-btn .badge {
  right: 50%;
  margin-right: 0;
}

.c-subheader-nav .c-subheader-nav-link:hover,
.c-subheader-nav .c-subheader-nav-btn:hover {
  text-decoration: none;
}

.c-subheader.c-subheader-dark {
  background: #3c4b64;
  border-bottom: 1px solid #636f83;
}

.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-link,
.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-btn {
  color: rgba(255, 255, 255, 0.75);
}

.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-link:hover, .c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-link:focus,
.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-btn:hover,
.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-btn:focus {
  color: rgba(255, 255, 255, 0.9);
}

.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-link.c-disabled,
.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-btn.c-disabled {
  color: rgba(255, 255, 255, 0.25);
}

.c-subheader.c-subheader-dark .c-subheader-nav .c-show > .c-subheader-nav-link,
.c-subheader.c-subheader-dark .c-subheader-nav .c-active > .c-subheader-nav-link,
.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-link.c-show,
.c-subheader.c-subheader-dark .c-subheader-nav .c-subheader-nav-link.c-active {
  color: #fff;
}

.c-subheader.c-subheader-dark .c-subheader-text {
  color: rgba(255, 255, 255, 0.75);
}

.c-subheader.c-subheader-dark .c-subheader-text a,.c-subheader.c-subheader-dark .c-subheader-text a:hover, .c-subheader.c-subheader-dark .c-subheader-text a:focus {
  color: #fff;
}

.c-subheader .c-subheader-nav .c-subheader-nav-link,
.c-subheader .c-subheader-nav .c-subheader-nav-btn {
  color: rgba(0, 0, 21, 0.5);
}

.c-subheader .c-subheader-nav .c-subheader-nav-link:hover, .c-subheader .c-subheader-nav .c-subheader-nav-link:focus,
.c-subheader .c-subheader-nav .c-subheader-nav-btn:hover,
.c-subheader .c-subheader-nav .c-subheader-nav-btn:focus {
  color: rgba(0, 0, 21, 0.7);
}

.c-subheader .c-subheader-nav .c-subheader-nav-link.c-disabled,
.c-subheader .c-subheader-nav .c-subheader-nav-btn.c-disabled {
  color: rgba(0, 0, 21, 0.3);
}

.c-subheader .c-subheader-nav .c-show > .c-subheader-nav-link,
.c-subheader .c-subheader-nav .c-active > .c-subheader-nav-link,
.c-subheader .c-subheader-nav .c-subheader-nav-link.c-show,
.c-subheader .c-subheader-nav .c-subheader-nav-link.c-active {
  color: rgba(0, 0, 21, 0.9);
}

.c-subheader .c-subheader-text {
  color: rgba(0, 0, 21, 0.5);
}

.c-subheader .c-subheader-text a,.c-subheader .c-subheader-text a:hover, .c-subheader .c-subheader-text a:focus {
  color: rgba(0, 0, 21, 0.9);
}

.c-switch {
  display: inline-block;
  width: 40px;
  height: 26px;
}

.c-switch-input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

.c-switch-slider {
  position: relative;
  display: block;
  height: inherit;
  cursor: pointer;
  border: 1px solid #d8dbe0;
  border-radius: 0.25rem;
}

.c-switch-slider,.c-switch-slider::before {
  background-color: #fff;
  transition: .15s ease-out;
}

.c-switch-slider::before {
  position: absolute;
  top: 2px;
  left: 2px;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  content: "";
  border: 1px solid #d8dbe0;
  border-radius: 0.125rem;
}

.c-switch-input:checked ~ .c-switch-slider::before {
  -webkit-transform: translateX(14px);
  transform: translateX(14px);
}

.c-switch-input:focus ~ .c-switch-slider {
  color: #768192;
  background-color: #fff;
  border-color: #958bef;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
}

.c-switch-input:disabled ~ .c-switch-slider {
  cursor: not-allowed;
  opacity: .5;
}

.c-switch-lg {
  width: 48px;
  height: 30px;
}

.c-switch-lg .c-switch-slider {
  font-size: 12px;
}

.c-switch-lg .c-switch-slider::before {
  width: 24px;
  height: 24px;
}

.c-switch-lg .c-switch-slider::after {
  font-size: 12px;
}

.c-switch-lg .c-switch-input:checked ~ .c-switch-slider::before {
  -webkit-transform: translateX(18px);
  transform: translateX(18px);
}

.c-switch-sm {
  width: 32px;
  height: 22px;
}

.c-switch-sm .c-switch-slider {
  font-size: 8px;
}

.c-switch-sm .c-switch-slider::before {
  width: 16px;
  height: 16px;
}

.c-switch-sm .c-switch-slider::after {
  font-size: 8px;
}

.c-switch-sm .c-switch-input:checked ~ .c-switch-slider::before {
  -webkit-transform: translateX(10px);
  transform: translateX(10px);
}

.c-switch-label {
  width: 48px;
}

.c-switch-label .c-switch-slider::before {
  z-index: 2;
}

.c-switch-label .c-switch-slider::after {
  position: absolute;
  top: 50%;
  z-index: 1;
  width: 50%;
  margin-top: -.5em;
  font-size: 10px;
  font-weight: 600;
  line-height: 1;
  color: #c4c9d0;
  text-align: center;
  text-transform: uppercase;
  content: attr(data-unchecked);
  transition: inherit;
}

html:not([dir="rtl"]) .c-switch-label .c-switch-slider::after {
  right: 1px;
}

.c-switch-label .c-switch-input:checked ~ .c-switch-slider::before {
  -webkit-transform: translateX(22px);
  transform: translateX(22px);
}

.c-switch-label .c-switch-input:checked ~ .c-switch-slider::after {
  left: 1px;
  color: #fff;
  content: attr(data-checked);
}

.c-switch-label.c-switch-lg {
  width: 56px;
  height: 30px;
}

.c-switch-label.c-switch-lg .c-switch-slider {
  font-size: 12px;
}

.c-switch-label.c-switch-lg .c-switch-slider::before {
  width: 24px;
  height: 24px;
}

.c-switch-label.c-switch-lg .c-switch-slider::after {
  font-size: 12px;
}

.c-switch-label.c-switch-lg .c-switch-input:checked ~ .c-switch-slider::before {
  -webkit-transform: translateX(26px);
  transform: translateX(26px);
}

.c-switch-label.c-switch-sm {
  width: 40px;
  height: 22px;
}

.c-switch-label.c-switch-sm .c-switch-slider {
  font-size: 8px;
}

.c-switch-label.c-switch-sm .c-switch-slider::before {
  width: 16px;
  height: 16px;
}

.c-switch-label.c-switch-sm .c-switch-slider::after {
  font-size: 8px;
}

.c-switch-label.c-switch-sm .c-switch-input:checked ~ .c-switch-slider::before {
  -webkit-transform: translateX(18px);
  transform: translateX(18px);
}

.c-switch[class*="-3d"] .c-switch-slider {
  background-color: #ebedef;
  border-radius: 50em;
}

.c-switch[class*="-3d"] .c-switch-slider::before {
  top: -1px;
  left: -1px;
  width: 26px;
  height: 26px;
  border: 0;
  border-radius: 50em;
  box-shadow: 0 2px 5px rgba(0, 0, 21, 0.3);
}

.c-switch[class*="-3d"].c-switch-lg {
  width: 48px;
  height: 30px;
}

.c-switch[class*="-3d"].c-switch-lg .c-switch-slider::before {
  width: 30px;
  height: 30px;
}

.c-switch[class*="-3d"].c-switch-lg .c-switch-input:checked ~ .c-switch-slider::before {
  -webkit-transform: translateX(18px);
  transform: translateX(18px);
}

.c-switch[class*="-3d"].c-switch-sm {
  width: 32px;
  height: 22px;
}

.c-switch[class*="-3d"].c-switch-sm .c-switch-slider::before {
  width: 22px;
  height: 22px;
}

.c-switch[class*="-3d"].c-switch-sm .c-switch-input:checked ~ .c-switch-slider::before {
  -webkit-transform: translateX(10px);
  transform: translateX(10px);
}

.c-switch-primary .c-switch-input:checked + .c-switch-slider {
  background-color: #321fdb;
  border-color: #2819ae;
}

.c-switch-primary .c-switch-input:checked + .c-switch-slider::before {
  border-color: #2819ae;
}

.c-switch-3d-primary .c-switch-input:checked + .c-switch-slider {
  background-color: #321fdb;
}

.c-switch-outline-primary .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #321fdb;
}

.c-switch-outline-primary .c-switch-input:checked + .c-switch-slider::before {
  border-color: #321fdb;
}

.c-switch-outline-primary .c-switch-input:checked + .c-switch-slider::after {
  color: #321fdb;
}

.c-switch-opposite-primary .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #321fdb;
}

.c-switch-opposite-primary .c-switch-input:checked + .c-switch-slider::before {
  background-color: #321fdb;
  border-color: #321fdb;
}

.c-switch-opposite-primary .c-switch-input:checked + .c-switch-slider::after {
  color: #321fdb;
}

.c-switch-secondary .c-switch-input:checked + .c-switch-slider {
  background-color: #ced2d8;
  border-color: #b2b8c1;
}

.c-switch-secondary .c-switch-input:checked + .c-switch-slider::before {
  border-color: #b2b8c1;
}

.c-switch-3d-secondary .c-switch-input:checked + .c-switch-slider {
  background-color: #ced2d8;
}

.c-switch-outline-secondary .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #ced2d8;
}

.c-switch-outline-secondary .c-switch-input:checked + .c-switch-slider::before {
  border-color: #ced2d8;
}

.c-switch-outline-secondary .c-switch-input:checked + .c-switch-slider::after {
  color: #ced2d8;
}

.c-switch-opposite-secondary .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #ced2d8;
}

.c-switch-opposite-secondary .c-switch-input:checked + .c-switch-slider::before {
  background-color: #ced2d8;
  border-color: #ced2d8;
}

.c-switch-opposite-secondary .c-switch-input:checked + .c-switch-slider::after {
  color: #ced2d8;
}

.c-switch-success .c-switch-input:checked + .c-switch-slider {
  background-color: #2eb85c;
  border-color: #248f48;
}

.c-switch-success .c-switch-input:checked + .c-switch-slider::before {
  border-color: #248f48;
}

.c-switch-3d-success .c-switch-input:checked + .c-switch-slider {
  background-color: #2eb85c;
}

.c-switch-outline-success .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #2eb85c;
}

.c-switch-outline-success .c-switch-input:checked + .c-switch-slider::before {
  border-color: #2eb85c;
}

.c-switch-outline-success .c-switch-input:checked + .c-switch-slider::after {
  color: #2eb85c;
}

.c-switch-opposite-success .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #2eb85c;
}

.c-switch-opposite-success .c-switch-input:checked + .c-switch-slider::before {
  background-color: #2eb85c;
  border-color: #2eb85c;
}

.c-switch-opposite-success .c-switch-input:checked + .c-switch-slider::after {
  color: #2eb85c;
}

.c-switch-info .c-switch-input:checked + .c-switch-slider {
  background-color: #39f;
  border-color: #0080ff;
}

.c-switch-info .c-switch-input:checked + .c-switch-slider::before {
  border-color: #0080ff;
}

.c-switch-3d-info .c-switch-input:checked + .c-switch-slider {
  background-color: #39f;
}

.c-switch-outline-info .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #39f;
}

.c-switch-outline-info .c-switch-input:checked + .c-switch-slider::before {
  border-color: #39f;
}

.c-switch-outline-info .c-switch-input:checked + .c-switch-slider::after {
  color: #39f;
}

.c-switch-opposite-info .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #39f;
}

.c-switch-opposite-info .c-switch-input:checked + .c-switch-slider::before {
  background-color: #39f;
  border-color: #39f;
}

.c-switch-opposite-info .c-switch-input:checked + .c-switch-slider::after {
  color: #39f;
}

.c-switch-warning .c-switch-input:checked + .c-switch-slider {
  background-color: #f9b115;
  border-color: #d69405;
}

.c-switch-warning .c-switch-input:checked + .c-switch-slider::before {
  border-color: #d69405;
}

.c-switch-3d-warning .c-switch-input:checked + .c-switch-slider {
  background-color: #f9b115;
}

.c-switch-outline-warning .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #f9b115;
}

.c-switch-outline-warning .c-switch-input:checked + .c-switch-slider::before {
  border-color: #f9b115;
}

.c-switch-outline-warning .c-switch-input:checked + .c-switch-slider::after {
  color: #f9b115;
}

.c-switch-opposite-warning .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #f9b115;
}

.c-switch-opposite-warning .c-switch-input:checked + .c-switch-slider::before {
  background-color: #f9b115;
  border-color: #f9b115;
}

.c-switch-opposite-warning .c-switch-input:checked + .c-switch-slider::after {
  color: #f9b115;
}

.c-switch-danger .c-switch-input:checked + .c-switch-slider {
  background-color: #e55353;
  border-color: #de2727;
}

.c-switch-danger .c-switch-input:checked + .c-switch-slider::before {
  border-color: #de2727;
}

.c-switch-3d-danger .c-switch-input:checked + .c-switch-slider {
  background-color: #e55353;
}

.c-switch-outline-danger .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #e55353;
}

.c-switch-outline-danger .c-switch-input:checked + .c-switch-slider::before {
  border-color: #e55353;
}

.c-switch-outline-danger .c-switch-input:checked + .c-switch-slider::after {
  color: #e55353;
}

.c-switch-opposite-danger .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #e55353;
}

.c-switch-opposite-danger .c-switch-input:checked + .c-switch-slider::before {
  background-color: #e55353;
  border-color: #e55353;
}

.c-switch-opposite-danger .c-switch-input:checked + .c-switch-slider::after {
  color: #e55353;
}

.c-switch-light .c-switch-input:checked + .c-switch-slider {
  background-color: #ebedef;
  border-color: #cfd4d8;
}

.c-switch-light .c-switch-input:checked + .c-switch-slider::before {
  border-color: #cfd4d8;
}

.c-switch-3d-light .c-switch-input:checked + .c-switch-slider {
  background-color: #ebedef;
}

.c-switch-outline-light .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #ebedef;
}

.c-switch-outline-light .c-switch-input:checked + .c-switch-slider::before {
  border-color: #ebedef;
}

.c-switch-outline-light .c-switch-input:checked + .c-switch-slider::after {
  color: #ebedef;
}

.c-switch-opposite-light .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #ebedef;
}

.c-switch-opposite-light .c-switch-input:checked + .c-switch-slider::before {
  background-color: #ebedef;
  border-color: #ebedef;
}

.c-switch-opposite-light .c-switch-input:checked + .c-switch-slider::after {
  color: #ebedef;
}

.c-switch-dark .c-switch-input:checked + .c-switch-slider {
  background-color: #636f83;
  border-color: #4d5666;
}

.c-switch-dark .c-switch-input:checked + .c-switch-slider::before {
  border-color: #4d5666;
}

.c-switch-3d-dark .c-switch-input:checked + .c-switch-slider {
  background-color: #636f83;
}

.c-switch-outline-dark .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #636f83;
}

.c-switch-outline-dark .c-switch-input:checked + .c-switch-slider::before {
  border-color: #636f83;
}

.c-switch-outline-dark .c-switch-input:checked + .c-switch-slider::after {
  color: #636f83;
}

.c-switch-opposite-dark .c-switch-input:checked + .c-switch-slider {
  background-color: #fff;
  border-color: #636f83;
}

.c-switch-opposite-dark .c-switch-input:checked + .c-switch-slider::before {
  background-color: #636f83;
  border-color: #636f83;
}

.c-switch-opposite-dark .c-switch-input:checked + .c-switch-slider::after {
  color: #636f83;
}

.c-switch-pill .c-switch-slider,.c-switch-pill .c-switch-slider::before {
  border-radius: 50em;
}

.c-switch-square .c-switch-slider,.c-switch-square .c-switch-slider::before {
  border-radius: 0;
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  color: #4f5d73;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid;
  border-top-color: #d8dbe0;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid;
  border-bottom-color: #d8dbe0;
}

.table tbody + tbody {
  border-top: 2px solid;
  border-top-color: #d8dbe0;
}

.table-sm th,
.table-sm td {
  padding: 0.3rem;
}

.table-bordered,.table-bordered th,
.table-bordered td {
  border: 1px solid;
  border-color: #d8dbe0;
}

.table-bordered thead th,
.table-bordered thead td {
  border-bottom-width: 2px;
}

.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
  border: 0;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 21, 0.05);
}

.table-hover tbody tr:hover {
  color: #4f5d73;
  background-color: rgba(0, 0, 21, 0.075);
}

.table-primary,
.table-primary > th,
.table-primary > td {
  color: #4f5d73;
  background-color: #c6c0f5;
}

.table-primary th,
.table-primary td,
.table-primary thead th,
.table-primary tbody + tbody {
  border-color: #948bec;
}

.table-hover .table-primary:hover,.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
  background-color: #b2aaf2;
}

.table-secondary,
.table-secondary > th,
.table-secondary > td {
  color: #4f5d73;
  background-color: #f1f2f4;
}

.table-secondary th,
.table-secondary td,
.table-secondary thead th,
.table-secondary tbody + tbody {
  border-color: #e6e8eb;
}

.table-hover .table-secondary:hover,.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
  background-color: #e3e5e9;
}

.table-success,
.table-success > th,
.table-success > td {
  color: #4f5d73;
  background-color: #c4ebd1;
}

.table-success th,
.table-success td,
.table-success thead th,
.table-success tbody + tbody {
  border-color: #92daaa;
}

.table-hover .table-success:hover,.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
  background-color: #b1e5c2;
}

.table-info,
.table-info > th,
.table-info > td {
  color: #4f5d73;
  background-color: #c6e2ff;
}

.table-info th,
.table-info td,
.table-info thead th,
.table-info tbody + tbody {
  border-color: #95caff;
}

.table-hover .table-info:hover,.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
  background-color: #add5ff;
}

.table-warning,
.table-warning > th,
.table-warning > td {
  color: #4f5d73;
  background-color: #fde9bd;
}

.table-warning th,
.table-warning td,
.table-warning thead th,
.table-warning tbody + tbody {
  border-color: #fcd685;
}

.table-hover .table-warning:hover,.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
  background-color: #fce1a4;
}

.table-danger,
.table-danger > th,
.table-danger > td {
  color: #4f5d73;
  background-color: #f8cfcf;
}

.table-danger th,
.table-danger td,
.table-danger thead th,
.table-danger tbody + tbody {
  border-color: #f1a6a6;
}

.table-hover .table-danger:hover,.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
  background-color: #f5b9b9;
}

.table-light,
.table-light > th,
.table-light > td {
  color: #4f5d73;
  background-color: #f9fafb;
}

.table-light th,
.table-light td,
.table-light thead th,
.table-light tbody + tbody {
  border-color: #f5f6f7;
}

.table-hover .table-light:hover,.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
  background-color: #eaedf1;
}

.table-dark,
.table-dark > th,
.table-dark > td {
  color: #4f5d73;
  background-color: #d3d7dc;
}

.table-dark th,
.table-dark td,
.table-dark thead th,
.table-dark tbody + tbody {
  border-color: #aeb4bf;
}

.table-hover .table-dark:hover,.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
  background-color: #c5cad1;
}

.table-active,
.table-active > th,
.table-active > td {
  color: #fff;
  background-color: rgba(0, 0, 21, 0.075);
}

.table-hover .table-active:hover,.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}

.table .thead-dark th {
  color: #fff;
  background-color: #636f83;
  border-color: #758297;
}

.table .thead-light th {
  color: #768192;
  background-color: #d8dbe0;
  border-color: #d8dbe0;
}

.table-dark {
  color: #fff;
  background-color: #636f83;
}

.table-dark th,
.table-dark td,
.table-dark thead th {
  border-color: #758297;
}

.table-dark.table-bordered {
  border: 0;
}

.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.table-dark.table-hover tbody tr:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.075);
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-sm > .table-bordered {
    border: 0;
  }
}

@media (max-width: 767.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-md > .table-bordered {
    border: 0;
  }
}

@media (max-width: 991.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-lg > .table-bordered {
    border: 0;
  }
}

@media (max-width: 1199.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive-xl > .table-bordered {
    border: 0;
  }
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive > .table-bordered {
  border: 0;
}

.table-outline {
  border: 1px solid;
  border-color: #d8dbe0;
}

.table-outline td,.table-align-middle td {
  vertical-align: middle;
}

.table-clear td {
  border: 0;
}

.toast {
  width: 350px;
  max-width: 350px;
  overflow: hidden;
  font-size: 0.875rem;
  background-clip: padding-box;
  border: 1px solid;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 21, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  opacity: 0;
  border-radius: 0.25rem;
  background-color: rgba(255, 255, 255, 0.85);
  border-color: rgba(0, 0, 21, 0.1);
}

.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}

.toast.showing,.toast.show {
  opacity: 1;
}

.toast.show {
  display: block;
}

.toast.hide {
  display: none;
}

.toast-full {
  width: 100%;
  max-width: 100%;
}

.toast-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background-clip: padding-box;
  border-bottom: 1px solid;
  color: #8a93a2;
  background-color: rgba(255, 255, 255, 0.85);
  border-color: rgba(0, 0, 21, 0.05);
}

.toast-body {
  padding: 0.75rem;
}

.toaster {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse;
  width: 100%;
  padding: 0.25rem 0.5rem;
}

.toaster-top-full, .toaster-top-center, .toaster-top-right, .toaster-top-left, .toaster-bottom-full, .toaster-bottom-center, .toaster-bottom-right, .toaster-bottom-left {
  position: fixed;
  z-index: 1080;
  width: 350px;
}

.toaster-top-full, .toaster-top-center, .toaster-top-right, .toaster-top-left {
  top: 0;
}

.toaster-bottom-full, .toaster-bottom-center, .toaster-bottom-right, .toaster-bottom-left {
  bottom: 0;
  -ms-flex-direction: column;
  flex-direction: column;
}

.toaster-top-full, .toaster-bottom-full {
  width: auto;
}

.toaster-top-center, .toaster-bottom-center {
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.toaster-top-full, .toaster-bottom-full, .toaster-top-right, .toaster-bottom-right {
  right: 0;
}

.toaster-top-full, .toaster-bottom-full, .toaster-top-left, .toaster-bottom-left {
  left: 0;
}

.toaster .toast {
  width: 100%;
  max-width: 100%;
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.765625rem;
  word-wrap: break-word;
  opacity: 0;
}

.tooltip.show {
  opacity: 0.9;
}

.tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}

.tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top, .bs-tooltip-auto[x-placement^="top"] {
  padding: 0.4rem 0;
}

.bs-tooltip-top .arrow, .bs-tooltip-auto[x-placement^="top"] .arrow {
  bottom: 0;
}

.bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^="top"] .arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000015;
}

.bs-tooltip-right, .bs-tooltip-auto[x-placement^="right"] {
  padding: 0 0.4rem;
}

.bs-tooltip-right .arrow, .bs-tooltip-auto[x-placement^="right"] .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^="right"] .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000015;
}

.bs-tooltip-bottom, .bs-tooltip-auto[x-placement^="bottom"] {
  padding: 0.4rem 0;
}

.bs-tooltip-bottom .arrow, .bs-tooltip-auto[x-placement^="bottom"] .arrow {
  top: 0;
}

.bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^="bottom"] .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000015;
}

.bs-tooltip-left, .bs-tooltip-auto[x-placement^="left"] {
  padding: 0 0.4rem;
}

.bs-tooltip-left .arrow, .bs-tooltip-auto[x-placement^="left"] .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^="left"] .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000015;
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000015;
  border-radius: 0.25rem;
}

.fade {
  transition: opacity 0.15s linear;
}

@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}

.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
}

.c-wrapper {
  will-change: auto;
  transition: margin-left 0.25s, margin-right 0.25s, width 0.25s, flex 0.25s, -webkit-transform 0.25s;
  transition: transform 0.25s, margin-left 0.25s, margin-right 0.25s, width 0.25s, flex 0.25s;
  transition: transform 0.25s, margin-left 0.25s, margin-right 0.25s, width 0.25s, flex 0.25s, -webkit-transform 0.25s, -ms-flex 0.25s;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
}

.c-sidebar.c-sidebar-unfoldable {
  transition: margin-left 0.25s, margin-right 0.25s, width 0.25s, z-index 0s ease 0s, -webkit-transform 0.25s;
  transition: transform 0.25s, margin-left 0.25s, margin-right 0.25s, width 0.25s, z-index 0s ease 0s;
  transition: transform 0.25s, margin-left 0.25s, margin-right 0.25s, width 0.25s, z-index 0s ease 0s, -webkit-transform 0.25s;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

h1, .h1 {
  font-size: 2.1875rem;
}

h2, .h2 {
  font-size: 1.75rem;
}

h3, .h3 {
  font-size: 1.53125rem;
}

h4, .h4 {
  font-size: 1.3125rem;
}

h5, .h5 {
  font-size: 1.09375rem;
}

h6, .h6 {
  font-size: 0.875rem;
}

.lead {
  font-size: 1.09375rem;
  font-weight: 300;
}

.display-1 {
  font-size: 6rem;
}

.display-1,.display-2 {
  font-weight: 300;
  line-height: 1.2;
}

.display-2 {
  font-size: 5.5rem;
}

.display-3 {
  font-size: 4.5rem;
}

.display-3,.display-4 {
  font-weight: 300;
  line-height: 1.2;
}

.display-4 {
  font-size: 3.5rem;
}

.c-vr {
  width: 1px;
  background-color: rgba(0, 0, 21, 0.2);
}

small,
.small {
  font-size: 80%;
  font-weight: 400;
}

mark,
.mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

.list-unstyled {
  list-style: none;
}

html:not([dir="rtl"]) .list-unstyled {
  padding-left: 0;
}

*[dir="rtl"] .list-unstyled {
  padding-right: 0;
}

.list-inline {
  list-style: none;
}

html:not([dir="rtl"]) .list-inline {
  padding-left: 0;
}

*[dir="rtl"] .list-inline {
  padding-right: 0;
}

.list-inline-item {
  display: inline-block;
}

.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 90%;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.09375rem;
}

.blockquote-footer {
  display: block;
  font-size: 80%;
  color: #8a93a2;
}

.blockquote-footer::before {
  content: "\2014\00A0";
}

@media all and (-ms-high-contrast: none) {
  html {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

.c-wrapper:not(.c-wrapper-fluid) {
  height: 100vh;
}

.c-wrapper:not(.c-wrapper-fluid) .c-header-fixed,
.c-wrapper:not(.c-wrapper-fluid) .c-subheader-fixed,
.c-wrapper:not(.c-wrapper-fluid) .c-footer-fixed {
  position: relative;
}

.c-wrapper:not(.c-wrapper-fluid) .c-body {
  -ms-flex-direction: column;
  flex-direction: column;
  overflow-y: auto;
}

.c-wrapper.c-wrapper-fluid {
  min-height: 100vh;
}

.c-wrapper.c-wrapper-fluid .c-header-fixed {
  margin: inherit;
}

.c-body {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.c-main {
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-width: 0;
  padding-top: 2rem;
}

@media (min-width: 768px) {
  .c-main > .container-fluid, .c-main > .container-sm, .c-main > .container-md, .c-main > .container-lg, .c-main > .container-xl {
    padding-right: 30px;
    padding-left: 30px;
  }
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.bg-primary {
  background-color: #321fdb !important;
}

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #2819ae !important;
}

.bg-secondary {
  background-color: #ced2d8 !important;
}

a.bg-secondary:hover, a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #b2b8c1 !important;
}

.bg-success {
  background-color: #2eb85c !important;
}

a.bg-success:hover, a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #248f48 !important;
}

.bg-info {
  background-color: #39f !important;
}

a.bg-info:hover, a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #0080ff !important;
}

.bg-warning {
  background-color: #f9b115 !important;
}

a.bg-warning:hover, a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #d69405 !important;
}

.bg-danger {
  background-color: #e55353 !important;
}

a.bg-danger:hover, a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #de2727 !important;
}

.bg-light {
  background-color: #ebedef !important;
}

a.bg-light:hover, a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #cfd4d8 !important;
}

.bg-dark {
  background-color: #636f83 !important;
}

a.bg-dark:hover, a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #4d5666 !important;
}

.bg-gradient-primary {
  background: #1f1498 !important;
  background: linear-gradient(45deg, #321fdb 0%, #1f1498 100%) !important;
  border-color: #1f1498 !important;
}

.c-dark-theme .bg-gradient-primary {
  background: #2d2587 !important;
  background: linear-gradient(45deg, #4638c2 0%, #2d2587 100%) !important;
  border-color: #2d2587 !important;
}

.bg-gradient-secondary {
  background: #fff !important;
  background: linear-gradient(45deg, #c8d2dc 0%, #fff 100%) !important;
  border-color: #fff !important;
}

.c-dark-theme .bg-gradient-secondary {
  background: white !important;
  background: linear-gradient(45deg, #d1d2d3 0%, white 100%) !important;
  border-color: white !important;
}

.bg-gradient-success {
  background: #1b9e3e !important;
  background: linear-gradient(45deg, #2eb85c 0%, #1b9e3e 100%) !important;
  border-color: #1b9e3e !important;
}

.c-dark-theme .bg-gradient-success {
  background: #2e8c47 !important;
  background: linear-gradient(45deg, #45a164 0%, #2e8c47 100%) !important;
  border-color: #2e8c47 !important;
}

.bg-gradient-info {
  background: #2982cc !important;
  background: linear-gradient(45deg, #39f 0%, #2982cc 100%) !important;
  border-color: #2982cc !important;
}

.c-dark-theme .bg-gradient-info {
  background: #4280b4 !important;
  background: linear-gradient(45deg, #4799eb 0%, #4280b4 100%) !important;
  border-color: #4280b4 !important;
}

.bg-gradient-warning {
  background: #f6960b !important;
  background: linear-gradient(45deg, #f9b115 0%, #f6960b 100%) !important;
  border-color: #f6960b !important;
}

.c-dark-theme .bg-gradient-warning {
  background: #dd9124 !important;
  background: linear-gradient(45deg, #e1a82d 0%, #dd9124 100%) !important;
  border-color: #dd9124 !important;
}

.bg-gradient-danger {
  background: #d93737 !important;
  background: linear-gradient(45deg, #e55353 0%, #d93737 100%) !important;
  border-color: #d93737 !important;
}

.c-dark-theme .bg-gradient-danger {
  background: #c14f4f !important;
  background: linear-gradient(45deg, #d16767 0%, #c14f4f 100%) !important;
  border-color: #c14f4f !important;
}

.bg-gradient-light {
  background: #fff !important;
  background: linear-gradient(45deg, #e3e8ed 0%, #fff 100%) !important;
  border-color: #fff !important;
}

.c-dark-theme .bg-gradient-light {
  background: white !important;
  background: linear-gradient(45deg, #e8e8e8 0%, white 100%) !important;
  border-color: white !important;
}

.bg-gradient-dark {
  background: #212333 !important;
  background: linear-gradient(45deg, #3c4b64 0%, #212333 100%) !important;
  border-color: #212333 !important;
}

.c-dark-theme .bg-gradient-dark {
  background: #292a2b !important;
  background: linear-gradient(45deg, #4c4f54 0%, #292a2b 100%) !important;
  border-color: #292a2b !important;
}

.bg-white {
  background-color: #fff !important;
}

.bg-transparent {
  background-color: transparent !important;
}

[class^="bg-"] {
  color: #fff;
}

.bg-facebook {
  background-color: #3b5998 !important;
}

a.bg-facebook:hover, a.bg-facebook:focus,
button.bg-facebook:hover,
button.bg-facebook:focus {
  background-color: #2d4373 !important;
}

.bg-twitter {
  background-color: #00aced !important;
}

a.bg-twitter:hover, a.bg-twitter:focus,
button.bg-twitter:hover,
button.bg-twitter:focus {
  background-color: #0087ba !important;
}

.bg-linkedin {
  background-color: #4875b4 !important;
}

a.bg-linkedin:hover, a.bg-linkedin:focus,
button.bg-linkedin:hover,
button.bg-linkedin:focus {
  background-color: #395d90 !important;
}

.bg-flickr {
  background-color: #ff0084 !important;
}

a.bg-flickr:hover, a.bg-flickr:focus,
button.bg-flickr:hover,
button.bg-flickr:focus {
  background-color: #cc006a !important;
}

.bg-tumblr {
  background-color: #32506d !important;
}

a.bg-tumblr:hover, a.bg-tumblr:focus,
button.bg-tumblr:hover,
button.bg-tumblr:focus {
  background-color: #22364a !important;
}

.bg-xing {
  background-color: #026466 !important;
}

a.bg-xing:hover, a.bg-xing:focus,
button.bg-xing:hover,
button.bg-xing:focus {
  background-color: #013334 !important;
}

.bg-github {
  background-color: #4183c4 !important;
}

a.bg-github:hover, a.bg-github:focus,
button.bg-github:hover,
button.bg-github:focus {
  background-color: #3269a0 !important;
}

.bg-stack-overflow {
  background-color: #fe7a15 !important;
}

a.bg-stack-overflow:hover, a.bg-stack-overflow:focus,
button.bg-stack-overflow:hover,
button.bg-stack-overflow:focus {
  background-color: #df6101 !important;
}

.bg-youtube {
  background-color: #b00 !important;
}

a.bg-youtube:hover, a.bg-youtube:focus,
button.bg-youtube:hover,
button.bg-youtube:focus {
  background-color: #880000 !important;
}

.bg-dribbble {
  background-color: #ea4c89 !important;
}

a.bg-dribbble:hover, a.bg-dribbble:focus,
button.bg-dribbble:hover,
button.bg-dribbble:focus {
  background-color: #e51e6b !important;
}

.bg-instagram {
  background-color: #517fa4 !important;
}

a.bg-instagram:hover, a.bg-instagram:focus,
button.bg-instagram:hover,
button.bg-instagram:focus {
  background-color: #406582 !important;
}

.bg-pinterest {
  background-color: #cb2027 !important;
}

a.bg-pinterest:hover, a.bg-pinterest:focus,
button.bg-pinterest:hover,
button.bg-pinterest:focus {
  background-color: #9f191f !important;
}

.bg-vk {
  background-color: #45668e !important;
}

a.bg-vk:hover, a.bg-vk:focus,
button.bg-vk:hover,
button.bg-vk:focus {
  background-color: #344d6c !important;
}

.bg-yahoo {
  background-color: #400191 !important;
}

a.bg-yahoo:hover, a.bg-yahoo:focus,
button.bg-yahoo:hover,
button.bg-yahoo:focus {
  background-color: #2a015e !important;
}

.bg-behance {
  background-color: #1769ff !important;
}

a.bg-behance:hover, a.bg-behance:focus,
button.bg-behance:hover,
button.bg-behance:focus {
  background-color: #0050e3 !important;
}

.bg-reddit {
  background-color: #ff4500 !important;
}

a.bg-reddit:hover, a.bg-reddit:focus,
button.bg-reddit:hover,
button.bg-reddit:focus {
  background-color: #cc3700 !important;
}

.bg-vimeo {
  background-color: #aad450 !important;
}

a.bg-vimeo:hover, a.bg-vimeo:focus,
button.bg-vimeo:hover,
button.bg-vimeo:focus {
  background-color: #93c130 !important;
}

.bg-gray-100 {
  background-color: #ebedef !important;
}

a.bg-gray-100:hover, a.bg-gray-100:focus,
button.bg-gray-100:hover,
button.bg-gray-100:focus {
  background-color: #cfd4d8 !important;
}

.bg-gray-200 {
  background-color: #d8dbe0 !important;
}

a.bg-gray-200:hover, a.bg-gray-200:focus,
button.bg-gray-200:hover,
button.bg-gray-200:focus {
  background-color: #bcc1c9 !important;
}

.bg-gray-300 {
  background-color: #c4c9d0 !important;
}

a.bg-gray-300:hover, a.bg-gray-300:focus,
button.bg-gray-300:hover,
button.bg-gray-300:focus {
  background-color: #a8afb9 !important;
}

.bg-gray-400 {
  background-color: #b1b7c1 !important;
}

a.bg-gray-400:hover, a.bg-gray-400:focus,
button.bg-gray-400:hover,
button.bg-gray-400:focus {
  background-color: #959daa !important;
}

.bg-gray-500 {
  background-color: #9da5b1 !important;
}

a.bg-gray-500:hover, a.bg-gray-500:focus,
button.bg-gray-500:hover,
button.bg-gray-500:focus {
  background-color: #818b9a !important;
}

.bg-gray-600 {
  background-color: #8a93a2 !important;
}

a.bg-gray-600:hover, a.bg-gray-600:focus,
button.bg-gray-600:hover,
button.bg-gray-600:focus {
  background-color: #6e798b !important;
}

.bg-gray-700 {
  background-color: #768192 !important;
}

a.bg-gray-700:hover, a.bg-gray-700:focus,
button.bg-gray-700:hover,
button.bg-gray-700:focus {
  background-color: #5e6877 !important;
}

.bg-gray-800 {
  background-color: #636f83 !important;
}

a.bg-gray-800:hover, a.bg-gray-800:focus,
button.bg-gray-800:hover,
button.bg-gray-800:focus {
  background-color: #4d5666 !important;
}

.bg-gray-900 {
  background-color: #4f5d73 !important;
}

a.bg-gray-900:hover, a.bg-gray-900:focus,
button.bg-gray-900:hover,
button.bg-gray-900:focus {
  background-color: #3a4555 !important;
}

.bg-box {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.border {
  border: 1px solid #d8dbe0 !important;
}

.border-top {
  border-top: 1px solid #d8dbe0 !important;
}

.border-right {
  border-right: 1px solid #d8dbe0 !important;
}

.border-bottom {
  border-bottom: 1px solid #d8dbe0 !important;
}

.border-left {
  border-left: 1px solid #d8dbe0 !important;
}

.border-0 {
  border: 0 !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-right-0 {
  border-right: 0 !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-left-0 {
  border-left: 0 !important;
}

.border-primary {
  border: 1px solid !important;
  border-color: #321fdb !important;
}

.border-secondary {
  border: 1px solid !important;
  border-color: #ced2d8 !important;
}

.border-success {
  border: 1px solid !important;
  border-color: #2eb85c !important;
}

.border-info {
  border: 1px solid !important;
  border-color: #39f !important;
}

.border-warning {
  border: 1px solid !important;
  border-color: #f9b115 !important;
}

.border-danger {
  border: 1px solid !important;
  border-color: #e55353 !important;
}

.border-light {
  border: 1px solid !important;
  border-color: #ebedef !important;
}

.border-dark {
  border: 1px solid !important;
  border-color: #636f83 !important;
}

.border-white {
  border-color: #fff !important;
}

.rounded-sm {
  border-radius: 0.2rem !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.rounded-top {
  border-top-left-radius: 0.25rem !important;
}

.rounded-top,.rounded-right {
  border-top-right-radius: 0.25rem !important;
}

.rounded-right,.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
}

.rounded-bottom,.rounded-left {
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-left {
  border-top-left-radius: 0.25rem !important;
}

.rounded-lg {
  border-radius: 0.3rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.b-a-0 {
  border: 0 !important;
}

.b-t-0 {
  border-top: 0 !important;
}

.b-r-0 {
  border-right: 0 !important;
}

.b-b-0 {
  border-bottom: 0 !important;
}

.b-l-0 {
  border-left: 0 !important;
}

.b-a-1 {
  border: 1px solid #d8dbe0;
}

.b-t-1 {
  border-top: 1px solid #d8dbe0;
}

.b-r-1 {
  border-right: 1px solid #d8dbe0;
}

.b-b-1 {
  border-bottom: 1px solid #d8dbe0;
}

.b-l-1 {
  border-left: 1px solid #d8dbe0;
}

.b-a-2 {
  border: 2px solid #d8dbe0;
}

.b-t-2 {
  border-top: 2px solid #d8dbe0;
}

.b-r-2 {
  border-right: 2px solid #d8dbe0;
}

.b-b-2 {
  border-bottom: 2px solid #d8dbe0;
}

.b-l-2 {
  border-left: 2px solid #d8dbe0;
}

.content-center {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 0;
  text-align: center;
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.d-none {
  display: none !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: -ms-flexbox !important;
  display: flex !important;
}

.d-inline-flex {
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-md-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (max-width: 575.98px) {
  .d-down-none {
    display: none !important;
  }
}

@media (max-width: 767.98px) {
  .d-sm-down-none {
    display: none !important;
  }
}

@media (max-width: 991.98px) {
  .d-md-down-none {
    display: none !important;
  }
}

@media (max-width: 1199.98px) {
  .d-lg-down-none {
    display: none !important;
  }
}

.d-xl-down-none,.c-default-theme .c-d-default-none {
  display: none !important;
}

@media print {
  .d-print-none {
    display: none !important;
  }
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .d-print-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}

.embed-responsive::before {
  display: block;
  content: "";
}

.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.embed-responsive-21by9::before {
  padding-top: 42.85714286%;
}

.embed-responsive-16by9::before {
  padding-top: 56.25%;
}

.embed-responsive-4by3::before {
  padding-top: 75%;
}

.embed-responsive-1by1::before {
  padding-top: 100%;
}

.flex-row {
  -ms-flex-direction: row !important;
  flex-direction: row !important;
}

.flex-column {
  -ms-flex-direction: column !important;
  flex-direction: column !important;
}

.flex-row-reverse {
  -ms-flex-direction: row-reverse !important;
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  -ms-flex-direction: column-reverse !important;
  flex-direction: column-reverse !important;
}

.flex-wrap {
  -ms-flex-wrap: wrap !important;
  flex-wrap: wrap !important;
}

.flex-nowrap {
  -ms-flex-wrap: nowrap !important;
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  -ms-flex: 1 1 auto !important;
  flex: 1 1 auto !important;
}

.flex-grow-0 {
  -ms-flex-positive: 0 !important;
  flex-grow: 0 !important;
}

.flex-grow-1 {
  -ms-flex-positive: 1 !important;
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  -ms-flex-negative: 0 !important;
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  -ms-flex-negative: 1 !important;
  flex-shrink: 1 !important;
}

.justify-content-start {
  -ms-flex-pack: start !important;
  justify-content: flex-start !important;
}

.justify-content-end {
  -ms-flex-pack: end !important;
  justify-content: flex-end !important;
}

.justify-content-center {
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

.justify-content-between {
  -ms-flex-pack: justify !important;
  justify-content: space-between !important;
}

.justify-content-around {
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important;
}

.align-items-start {
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}

.align-items-end {
  -ms-flex-align: end !important;
  align-items: flex-end !important;
}

.align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
}

.align-items-baseline {
  -ms-flex-align: baseline !important;
  align-items: baseline !important;
}

.align-items-stretch {
  -ms-flex-align: stretch !important;
  align-items: stretch !important;
}

.align-content-start {
  -ms-flex-line-pack: start !important;
  align-content: flex-start !important;
}

.align-content-end {
  -ms-flex-line-pack: end !important;
  align-content: flex-end !important;
}

.align-content-center {
  -ms-flex-line-pack: center !important;
  align-content: center !important;
}

.align-content-between {
  -ms-flex-line-pack: justify !important;
  align-content: space-between !important;
}

.align-content-around {
  -ms-flex-line-pack: distribute !important;
  align-content: space-around !important;
}

.align-content-stretch {
  -ms-flex-line-pack: stretch !important;
  align-content: stretch !important;
}

.align-self-auto {
  -ms-flex-item-align: auto !important;
  align-self: auto !important;
}

.align-self-start {
  -ms-flex-item-align: start !important;
  align-self: flex-start !important;
}

.align-self-end {
  -ms-flex-item-align: end !important;
  align-self: flex-end !important;
}

.align-self-center {
  -ms-flex-item-align: center !important;
  align-self: center !important;
}

.align-self-baseline {
  -ms-flex-item-align: baseline !important;
  align-self: baseline !important;
}

.align-self-stretch {
  -ms-flex-item-align: stretch !important;
  align-self: stretch !important;
}

@media (min-width: 576px) {
  .flex-sm-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .flex-sm-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .flex-sm-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .flex-sm-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .flex-sm-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .justify-content-sm-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .justify-content-sm-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .align-items-sm-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .align-items-sm-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .align-content-sm-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .align-content-sm-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .align-content-sm-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .align-self-sm-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .align-self-sm-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 768px) {
  .flex-md-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .flex-md-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .flex-md-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .flex-md-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .flex-md-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .justify-content-md-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .justify-content-md-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .align-items-md-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .align-items-md-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .align-items-md-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .align-items-md-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .align-content-md-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .align-content-md-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .align-content-md-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .align-content-md-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .align-content-md-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .align-self-md-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .align-self-md-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .align-self-md-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .align-self-md-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .align-self-md-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 992px) {
  .flex-lg-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .flex-lg-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .flex-lg-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .flex-lg-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .flex-lg-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .justify-content-lg-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .justify-content-lg-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .align-items-lg-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .align-items-lg-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .align-content-lg-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .align-content-lg-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .align-content-lg-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .align-self-lg-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .align-self-lg-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 1200px) {
  .flex-xl-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .flex-xl-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .flex-xl-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .flex-xl-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .flex-xl-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .justify-content-xl-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .justify-content-xl-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .align-items-xl-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .align-items-xl-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .align-content-xl-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .align-content-xl-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .align-content-xl-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .align-self-xl-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .align-self-xl-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

.float-left {
  float: left !important;
}

.float-right {
  float: right !important;
}

.float-none {
  float: none !important;
}

@media (min-width: 576px) {
  .float-sm-left {
    float: left !important;
  }
  .float-sm-right {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
}

@media (min-width: 768px) {
  .float-md-left {
    float: left !important;
  }
  .float-md-right {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
}

@media (min-width: 992px) {
  .float-lg-left {
    float: left !important;
  }
  .float-lg-right {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
}

@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important;
  }
  .float-xl-right {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

.fixed-top {
  top: 0;
}

.fixed-top,.fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  bottom: 0;
}

@supports ((position: -webkit-sticky) or (position: sticky)) {
  .sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 21, 0.075) !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 21, 0.15) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 21, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.mh-100 {
  max-height: 100% !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.vw-100 {
  width: 100vw !important;
}

.vh-100 {
  height: 100vh !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: rgba(0, 0, 21, 0);
}

.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0,html:not([dir="rtl"]) .mfs-0 {
  margin-left: 0 !important;
}

*[dir="rtl"] .mfs-0,html:not([dir="rtl"]) .mfe-0 {
  margin-right: 0 !important;
}

*[dir="rtl"] .mfe-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1,html:not([dir="rtl"]) .mfs-1 {
  margin-left: 0.25rem !important;
}

*[dir="rtl"] .mfs-1,html:not([dir="rtl"]) .mfe-1 {
  margin-right: 0.25rem !important;
}

*[dir="rtl"] .mfe-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2,html:not([dir="rtl"]) .mfs-2 {
  margin-left: 0.5rem !important;
}

*[dir="rtl"] .mfs-2,html:not([dir="rtl"]) .mfe-2 {
  margin-right: 0.5rem !important;
}

*[dir="rtl"] .mfe-2 {
  margin-left: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.mt-3,
.my-3 {
  margin-top: 1rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.ml-3,
.mx-3,html:not([dir="rtl"]) .mfs-3 {
  margin-left: 1rem !important;
}

*[dir="rtl"] .mfs-3,html:not([dir="rtl"]) .mfe-3 {
  margin-right: 1rem !important;
}

*[dir="rtl"] .mfe-3 {
  margin-left: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4,
.mx-4,html:not([dir="rtl"]) .mfs-4 {
  margin-left: 1.5rem !important;
}

*[dir="rtl"] .mfs-4,html:not([dir="rtl"]) .mfe-4 {
  margin-right: 1.5rem !important;
}

*[dir="rtl"] .mfe-4 {
  margin-left: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.mt-5,
.my-5 {
  margin-top: 3rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}

.ml-5,
.mx-5,html:not([dir="rtl"]) .mfs-5 {
  margin-left: 3rem !important;
}

*[dir="rtl"] .mfs-5,html:not([dir="rtl"]) .mfe-5 {
  margin-right: 3rem !important;
}

*[dir="rtl"] .mfe-5 {
  margin-left: 3rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0,html:not([dir="rtl"]) .pfs-0 {
  padding-left: 0 !important;
}

*[dir="rtl"] .pfs-0,html:not([dir="rtl"]) .pfe-0 {
  padding-right: 0 !important;
}

*[dir="rtl"] .pfe-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1,html:not([dir="rtl"]) .pfs-1 {
  padding-left: 0.25rem !important;
}

*[dir="rtl"] .pfs-1,html:not([dir="rtl"]) .pfe-1 {
  padding-right: 0.25rem !important;
}

*[dir="rtl"] .pfe-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}

.pl-2,
.px-2,html:not([dir="rtl"]) .pfs-2 {
  padding-left: 0.5rem !important;
}

*[dir="rtl"] .pfs-2,html:not([dir="rtl"]) .pfe-2 {
  padding-right: 0.5rem !important;
}

*[dir="rtl"] .pfe-2 {
  padding-left: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pt-3,
.py-3 {
  padding-top: 1rem !important;
}

.pr-3,
.px-3 {
  padding-right: 1rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}

.pl-3,
.px-3,html:not([dir="rtl"]) .pfs-3 {
  padding-left: 1rem !important;
}

*[dir="rtl"] .pfs-3,html:not([dir="rtl"]) .pfe-3 {
  padding-right: 1rem !important;
}

*[dir="rtl"] .pfe-3 {
  padding-left: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}

.pl-4,
.px-4,html:not([dir="rtl"]) .pfs-4 {
  padding-left: 1.5rem !important;
}

*[dir="rtl"] .pfs-4,html:not([dir="rtl"]) .pfe-4 {
  padding-right: 1.5rem !important;
}

*[dir="rtl"] .pfe-4 {
  padding-left: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.pt-5,
.py-5 {
  padding-top: 3rem !important;
}

.pr-5,
.px-5 {
  padding-right: 3rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}

.pl-5,
.px-5,html:not([dir="rtl"]) .pfs-5 {
  padding-left: 3rem !important;
}

*[dir="rtl"] .pfs-5,html:not([dir="rtl"]) .pfe-5 {
  padding-right: 3rem !important;
}

*[dir="rtl"] .pfe-5 {
  padding-left: 3rem !important;
}

.m-n1 {
  margin: -0.25rem !important;
}

.mt-n1,
.my-n1 {
  margin-top: -0.25rem !important;
}

.mr-n1,
.mx-n1 {
  margin-right: -0.25rem !important;
}

.mb-n1,
.my-n1 {
  margin-bottom: -0.25rem !important;
}

.ml-n1,
.mx-n1,html:not([dir="rtl"]) .mfs-n1 {
  margin-left: -0.25rem !important;
}

*[dir="rtl"] .mfs-n1,html:not([dir="rtl"]) .mfe-n1 {
  margin-right: -0.25rem !important;
}

*[dir="rtl"] .mfe-n1 {
  margin-left: -0.25rem !important;
}

.m-n2 {
  margin: -0.5rem !important;
}

.mt-n2,
.my-n2 {
  margin-top: -0.5rem !important;
}

.mr-n2,
.mx-n2 {
  margin-right: -0.5rem !important;
}

.mb-n2,
.my-n2 {
  margin-bottom: -0.5rem !important;
}

.ml-n2,
.mx-n2,html:not([dir="rtl"]) .mfs-n2 {
  margin-left: -0.5rem !important;
}

*[dir="rtl"] .mfs-n2,html:not([dir="rtl"]) .mfe-n2 {
  margin-right: -0.5rem !important;
}

*[dir="rtl"] .mfe-n2 {
  margin-left: -0.5rem !important;
}

.m-n3 {
  margin: -1rem !important;
}

.mt-n3,
.my-n3 {
  margin-top: -1rem !important;
}

.mr-n3,
.mx-n3 {
  margin-right: -1rem !important;
}

.mb-n3,
.my-n3 {
  margin-bottom: -1rem !important;
}

.ml-n3,
.mx-n3,html:not([dir="rtl"]) .mfs-n3 {
  margin-left: -1rem !important;
}

*[dir="rtl"] .mfs-n3,html:not([dir="rtl"]) .mfe-n3 {
  margin-right: -1rem !important;
}

*[dir="rtl"] .mfe-n3 {
  margin-left: -1rem !important;
}

.m-n4 {
  margin: -1.5rem !important;
}

.mt-n4,
.my-n4 {
  margin-top: -1.5rem !important;
}

.mr-n4,
.mx-n4 {
  margin-right: -1.5rem !important;
}

.mb-n4,
.my-n4 {
  margin-bottom: -1.5rem !important;
}

.ml-n4,
.mx-n4,html:not([dir="rtl"]) .mfs-n4 {
  margin-left: -1.5rem !important;
}

*[dir="rtl"] .mfs-n4,html:not([dir="rtl"]) .mfe-n4 {
  margin-right: -1.5rem !important;
}

*[dir="rtl"] .mfe-n4 {
  margin-left: -1.5rem !important;
}

.m-n5 {
  margin: -3rem !important;
}

.mt-n5,
.my-n5 {
  margin-top: -3rem !important;
}

.mr-n5,
.mx-n5 {
  margin-right: -3rem !important;
}

.mb-n5,
.my-n5 {
  margin-bottom: -3rem !important;
}

.ml-n5,
.mx-n5,html:not([dir="rtl"]) .mfs-n5 {
  margin-left: -3rem !important;
}

*[dir="rtl"] .mfs-n5,html:not([dir="rtl"]) .mfe-n5 {
  margin-right: -3rem !important;
}

*[dir="rtl"] .mfe-n5 {
  margin-left: -3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mt-auto,
.my-auto {
  margin-top: auto !important;
}

.mr-auto,
.mx-auto {
  margin-right: auto !important;
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important;
}

.ml-auto,
.mx-auto,html:not([dir="rtl"]) .mfs-auto {
  margin-left: auto !important;
}

*[dir="rtl"] .mfs-auto,html:not([dir="rtl"]) .mfe-auto {
  margin-right: auto !important;
}

*[dir="rtl"] .mfe-auto {
  margin-left: auto !important;
}

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important;
  }
  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important;
  }
  .mr-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important;
  }
  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important;
  }
  .ml-sm-0,
  .mx-sm-0,html:not([dir="rtl"]) .mfs-sm-0 {
    margin-left: 0 !important;
  }
  *[dir="rtl"] .mfs-sm-0,html:not([dir="rtl"]) .mfe-sm-0 {
    margin-right: 0 !important;
  }
  *[dir="rtl"] .mfe-sm-0 {
    margin-left: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .mt-sm-1,
  .my-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mr-sm-1,
  .mx-sm-1 {
    margin-right: 0.25rem !important;
  }
  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-sm-1,
  .mx-sm-1,html:not([dir="rtl"]) .mfs-sm-1 {
    margin-left: 0.25rem !important;
  }
  *[dir="rtl"] .mfs-sm-1,html:not([dir="rtl"]) .mfe-sm-1 {
    margin-right: 0.25rem !important;
  }
  *[dir="rtl"] .mfe-sm-1 {
    margin-left: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .mt-sm-2,
  .my-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mr-sm-2,
  .mx-sm-2 {
    margin-right: 0.5rem !important;
  }
  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-sm-2,
  .mx-sm-2,html:not([dir="rtl"]) .mfs-sm-2 {
    margin-left: 0.5rem !important;
  }
  *[dir="rtl"] .mfs-sm-2,html:not([dir="rtl"]) .mfe-sm-2 {
    margin-right: 0.5rem !important;
  }
  *[dir="rtl"] .mfe-sm-2 {
    margin-left: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .mt-sm-3,
  .my-sm-3 {
    margin-top: 1rem !important;
  }
  .mr-sm-3,
  .mx-sm-3 {
    margin-right: 1rem !important;
  }
  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 1rem !important;
  }
  .ml-sm-3,
  .mx-sm-3,html:not([dir="rtl"]) .mfs-sm-3 {
    margin-left: 1rem !important;
  }
  *[dir="rtl"] .mfs-sm-3,html:not([dir="rtl"]) .mfe-sm-3 {
    margin-right: 1rem !important;
  }
  *[dir="rtl"] .mfe-sm-3 {
    margin-left: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .mt-sm-4,
  .my-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mr-sm-4,
  .mx-sm-4 {
    margin-right: 1.5rem !important;
  }
  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-sm-4,
  .mx-sm-4,html:not([dir="rtl"]) .mfs-sm-4 {
    margin-left: 1.5rem !important;
  }
  *[dir="rtl"] .mfs-sm-4,html:not([dir="rtl"]) .mfe-sm-4 {
    margin-right: 1.5rem !important;
  }
  *[dir="rtl"] .mfe-sm-4 {
    margin-left: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .mt-sm-5,
  .my-sm-5 {
    margin-top: 3rem !important;
  }
  .mr-sm-5,
  .mx-sm-5 {
    margin-right: 3rem !important;
  }
  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 3rem !important;
  }
  .ml-sm-5,
  .mx-sm-5,html:not([dir="rtl"]) .mfs-sm-5 {
    margin-left: 3rem !important;
  }
  *[dir="rtl"] .mfs-sm-5,html:not([dir="rtl"]) .mfe-sm-5 {
    margin-right: 3rem !important;
  }
  *[dir="rtl"] .mfe-sm-5 {
    margin-left: 3rem !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important;
  }
  .pr-sm-0,
  .px-sm-0 {
    padding-right: 0 !important;
  }
  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important;
  }
  .pl-sm-0,
  .px-sm-0,html:not([dir="rtl"]) .pfs-sm-0 {
    padding-left: 0 !important;
  }
  *[dir="rtl"] .pfs-sm-0,html:not([dir="rtl"]) .pfe-sm-0 {
    padding-right: 0 !important;
  }
  *[dir="rtl"] .pfe-sm-0 {
    padding-left: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .pt-sm-1,
  .py-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pr-sm-1,
  .px-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-sm-1,
  .px-sm-1,html:not([dir="rtl"]) .pfs-sm-1 {
    padding-left: 0.25rem !important;
  }
  *[dir="rtl"] .pfs-sm-1,html:not([dir="rtl"]) .pfe-sm-1 {
    padding-right: 0.25rem !important;
  }
  *[dir="rtl"] .pfe-sm-1 {
    padding-left: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .pt-sm-2,
  .py-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pr-sm-2,
  .px-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-sm-2,
  .px-sm-2,html:not([dir="rtl"]) .pfs-sm-2 {
    padding-left: 0.5rem !important;
  }
  *[dir="rtl"] .pfs-sm-2,html:not([dir="rtl"]) .pfe-sm-2 {
    padding-right: 0.5rem !important;
  }
  *[dir="rtl"] .pfe-sm-2 {
    padding-left: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .pt-sm-3,
  .py-sm-3 {
    padding-top: 1rem !important;
  }
  .pr-sm-3,
  .px-sm-3 {
    padding-right: 1rem !important;
  }
  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pl-sm-3,
  .px-sm-3,html:not([dir="rtl"]) .pfs-sm-3 {
    padding-left: 1rem !important;
  }
  *[dir="rtl"] .pfs-sm-3,html:not([dir="rtl"]) .pfe-sm-3 {
    padding-right: 1rem !important;
  }
  *[dir="rtl"] .pfe-sm-3 {
    padding-left: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .pt-sm-4,
  .py-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pr-sm-4,
  .px-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-sm-4,
  .px-sm-4,html:not([dir="rtl"]) .pfs-sm-4 {
    padding-left: 1.5rem !important;
  }
  *[dir="rtl"] .pfs-sm-4,html:not([dir="rtl"]) .pfe-sm-4 {
    padding-right: 1.5rem !important;
  }
  *[dir="rtl"] .pfe-sm-4 {
    padding-left: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .pt-sm-5,
  .py-sm-5 {
    padding-top: 3rem !important;
  }
  .pr-sm-5,
  .px-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 3rem !important;
  }
  .pl-sm-5,
  .px-sm-5,html:not([dir="rtl"]) .pfs-sm-5 {
    padding-left: 3rem !important;
  }
  *[dir="rtl"] .pfs-sm-5,html:not([dir="rtl"]) .pfe-sm-5 {
    padding-right: 3rem !important;
  }
  *[dir="rtl"] .pfe-sm-5 {
    padding-left: 3rem !important;
  }
  .m-sm-n1 {
    margin: -0.25rem !important;
  }
  .mt-sm-n1,
  .my-sm-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-sm-n1,
  .mx-sm-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-sm-n1,
  .my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-sm-n1,
  .mx-sm-n1,html:not([dir="rtl"]) .mfs-sm-n1 {
    margin-left: -0.25rem !important;
  }
  *[dir="rtl"] .mfs-sm-n1,html:not([dir="rtl"]) .mfe-sm-n1 {
    margin-right: -0.25rem !important;
  }
  *[dir="rtl"] .mfe-sm-n1 {
    margin-left: -0.25rem !important;
  }
  .m-sm-n2 {
    margin: -0.5rem !important;
  }
  .mt-sm-n2,
  .my-sm-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-sm-n2,
  .mx-sm-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-sm-n2,
  .my-sm-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-sm-n2,
  .mx-sm-n2,html:not([dir="rtl"]) .mfs-sm-n2 {
    margin-left: -0.5rem !important;
  }
  *[dir="rtl"] .mfs-sm-n2,html:not([dir="rtl"]) .mfe-sm-n2 {
    margin-right: -0.5rem !important;
  }
  *[dir="rtl"] .mfe-sm-n2 {
    margin-left: -0.5rem !important;
  }
  .m-sm-n3 {
    margin: -1rem !important;
  }
  .mt-sm-n3,
  .my-sm-n3 {
    margin-top: -1rem !important;
  }
  .mr-sm-n3,
  .mx-sm-n3 {
    margin-right: -1rem !important;
  }
  .mb-sm-n3,
  .my-sm-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-sm-n3,
  .mx-sm-n3,html:not([dir="rtl"]) .mfs-sm-n3 {
    margin-left: -1rem !important;
  }
  *[dir="rtl"] .mfs-sm-n3,html:not([dir="rtl"]) .mfe-sm-n3 {
    margin-right: -1rem !important;
  }
  *[dir="rtl"] .mfe-sm-n3 {
    margin-left: -1rem !important;
  }
  .m-sm-n4 {
    margin: -1.5rem !important;
  }
  .mt-sm-n4,
  .my-sm-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-sm-n4,
  .mx-sm-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-sm-n4,
  .my-sm-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-sm-n4,
  .mx-sm-n4,html:not([dir="rtl"]) .mfs-sm-n4 {
    margin-left: -1.5rem !important;
  }
  *[dir="rtl"] .mfs-sm-n4,html:not([dir="rtl"]) .mfe-sm-n4 {
    margin-right: -1.5rem !important;
  }
  *[dir="rtl"] .mfe-sm-n4 {
    margin-left: -1.5rem !important;
  }
  .m-sm-n5 {
    margin: -3rem !important;
  }
  .mt-sm-n5,
  .my-sm-n5 {
    margin-top: -3rem !important;
  }
  .mr-sm-n5,
  .mx-sm-n5 {
    margin-right: -3rem !important;
  }
  .mb-sm-n5,
  .my-sm-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-sm-n5,
  .mx-sm-n5,html:not([dir="rtl"]) .mfs-sm-n5 {
    margin-left: -3rem !important;
  }
  *[dir="rtl"] .mfs-sm-n5,html:not([dir="rtl"]) .mfe-sm-n5 {
    margin-right: -3rem !important;
  }
  *[dir="rtl"] .mfe-sm-n5 {
    margin-left: -3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important;
  }
  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important;
  }
  .ml-sm-auto,
  .mx-sm-auto,html:not([dir="rtl"]) .mfs-sm-auto {
    margin-left: auto !important;
  }
  *[dir="rtl"] .mfs-sm-auto,html:not([dir="rtl"]) .mfe-sm-auto {
    margin-right: auto !important;
  }
  *[dir="rtl"] .mfe-sm-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important;
  }
  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important;
  }
  .mr-md-0,
  .mx-md-0 {
    margin-right: 0 !important;
  }
  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important;
  }
  .ml-md-0,
  .mx-md-0,html:not([dir="rtl"]) .mfs-md-0 {
    margin-left: 0 !important;
  }
  *[dir="rtl"] .mfs-md-0,html:not([dir="rtl"]) .mfe-md-0 {
    margin-right: 0 !important;
  }
  *[dir="rtl"] .mfe-md-0 {
    margin-left: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .mt-md-1,
  .my-md-1 {
    margin-top: 0.25rem !important;
  }
  .mr-md-1,
  .mx-md-1 {
    margin-right: 0.25rem !important;
  }
  .mb-md-1,
  .my-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-md-1,
  .mx-md-1,html:not([dir="rtl"]) .mfs-md-1 {
    margin-left: 0.25rem !important;
  }
  *[dir="rtl"] .mfs-md-1,html:not([dir="rtl"]) .mfe-md-1 {
    margin-right: 0.25rem !important;
  }
  *[dir="rtl"] .mfe-md-1 {
    margin-left: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .mt-md-2,
  .my-md-2 {
    margin-top: 0.5rem !important;
  }
  .mr-md-2,
  .mx-md-2 {
    margin-right: 0.5rem !important;
  }
  .mb-md-2,
  .my-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-md-2,
  .mx-md-2,html:not([dir="rtl"]) .mfs-md-2 {
    margin-left: 0.5rem !important;
  }
  *[dir="rtl"] .mfs-md-2,html:not([dir="rtl"]) .mfe-md-2 {
    margin-right: 0.5rem !important;
  }
  *[dir="rtl"] .mfe-md-2 {
    margin-left: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .mt-md-3,
  .my-md-3 {
    margin-top: 1rem !important;
  }
  .mr-md-3,
  .mx-md-3 {
    margin-right: 1rem !important;
  }
  .mb-md-3,
  .my-md-3 {
    margin-bottom: 1rem !important;
  }
  .ml-md-3,
  .mx-md-3,html:not([dir="rtl"]) .mfs-md-3 {
    margin-left: 1rem !important;
  }
  *[dir="rtl"] .mfs-md-3,html:not([dir="rtl"]) .mfe-md-3 {
    margin-right: 1rem !important;
  }
  *[dir="rtl"] .mfe-md-3 {
    margin-left: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .mt-md-4,
  .my-md-4 {
    margin-top: 1.5rem !important;
  }
  .mr-md-4,
  .mx-md-4 {
    margin-right: 1.5rem !important;
  }
  .mb-md-4,
  .my-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-md-4,
  .mx-md-4,html:not([dir="rtl"]) .mfs-md-4 {
    margin-left: 1.5rem !important;
  }
  *[dir="rtl"] .mfs-md-4,html:not([dir="rtl"]) .mfe-md-4 {
    margin-right: 1.5rem !important;
  }
  *[dir="rtl"] .mfe-md-4 {
    margin-left: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .mt-md-5,
  .my-md-5 {
    margin-top: 3rem !important;
  }
  .mr-md-5,
  .mx-md-5 {
    margin-right: 3rem !important;
  }
  .mb-md-5,
  .my-md-5 {
    margin-bottom: 3rem !important;
  }
  .ml-md-5,
  .mx-md-5,html:not([dir="rtl"]) .mfs-md-5 {
    margin-left: 3rem !important;
  }
  *[dir="rtl"] .mfs-md-5,html:not([dir="rtl"]) .mfe-md-5 {
    margin-right: 3rem !important;
  }
  *[dir="rtl"] .mfe-md-5 {
    margin-left: 3rem !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important;
  }
  .pr-md-0,
  .px-md-0 {
    padding-right: 0 !important;
  }
  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important;
  }
  .pl-md-0,
  .px-md-0,html:not([dir="rtl"]) .pfs-md-0 {
    padding-left: 0 !important;
  }
  *[dir="rtl"] .pfs-md-0,html:not([dir="rtl"]) .pfe-md-0 {
    padding-right: 0 !important;
  }
  *[dir="rtl"] .pfe-md-0 {
    padding-left: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .pt-md-1,
  .py-md-1 {
    padding-top: 0.25rem !important;
  }
  .pr-md-1,
  .px-md-1 {
    padding-right: 0.25rem !important;
  }
  .pb-md-1,
  .py-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-md-1,
  .px-md-1,html:not([dir="rtl"]) .pfs-md-1 {
    padding-left: 0.25rem !important;
  }
  *[dir="rtl"] .pfs-md-1,html:not([dir="rtl"]) .pfe-md-1 {
    padding-right: 0.25rem !important;
  }
  *[dir="rtl"] .pfe-md-1 {
    padding-left: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .pt-md-2,
  .py-md-2 {
    padding-top: 0.5rem !important;
  }
  .pr-md-2,
  .px-md-2 {
    padding-right: 0.5rem !important;
  }
  .pb-md-2,
  .py-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-md-2,
  .px-md-2,html:not([dir="rtl"]) .pfs-md-2 {
    padding-left: 0.5rem !important;
  }
  *[dir="rtl"] .pfs-md-2,html:not([dir="rtl"]) .pfe-md-2 {
    padding-right: 0.5rem !important;
  }
  *[dir="rtl"] .pfe-md-2 {
    padding-left: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .pt-md-3,
  .py-md-3 {
    padding-top: 1rem !important;
  }
  .pr-md-3,
  .px-md-3 {
    padding-right: 1rem !important;
  }
  .pb-md-3,
  .py-md-3 {
    padding-bottom: 1rem !important;
  }
  .pl-md-3,
  .px-md-3,html:not([dir="rtl"]) .pfs-md-3 {
    padding-left: 1rem !important;
  }
  *[dir="rtl"] .pfs-md-3,html:not([dir="rtl"]) .pfe-md-3 {
    padding-right: 1rem !important;
  }
  *[dir="rtl"] .pfe-md-3 {
    padding-left: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .pt-md-4,
  .py-md-4 {
    padding-top: 1.5rem !important;
  }
  .pr-md-4,
  .px-md-4 {
    padding-right: 1.5rem !important;
  }
  .pb-md-4,
  .py-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-md-4,
  .px-md-4,html:not([dir="rtl"]) .pfs-md-4 {
    padding-left: 1.5rem !important;
  }
  *[dir="rtl"] .pfs-md-4,html:not([dir="rtl"]) .pfe-md-4 {
    padding-right: 1.5rem !important;
  }
  *[dir="rtl"] .pfe-md-4 {
    padding-left: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .pt-md-5,
  .py-md-5 {
    padding-top: 3rem !important;
  }
  .pr-md-5,
  .px-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-5,
  .py-md-5 {
    padding-bottom: 3rem !important;
  }
  .pl-md-5,
  .px-md-5,html:not([dir="rtl"]) .pfs-md-5 {
    padding-left: 3rem !important;
  }
  *[dir="rtl"] .pfs-md-5,html:not([dir="rtl"]) .pfe-md-5 {
    padding-right: 3rem !important;
  }
  *[dir="rtl"] .pfe-md-5 {
    padding-left: 3rem !important;
  }
  .m-md-n1 {
    margin: -0.25rem !important;
  }
  .mt-md-n1,
  .my-md-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-md-n1,
  .mx-md-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-md-n1,
  .my-md-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-md-n1,
  .mx-md-n1,html:not([dir="rtl"]) .mfs-md-n1 {
    margin-left: -0.25rem !important;
  }
  *[dir="rtl"] .mfs-md-n1,html:not([dir="rtl"]) .mfe-md-n1 {
    margin-right: -0.25rem !important;
  }
  *[dir="rtl"] .mfe-md-n1 {
    margin-left: -0.25rem !important;
  }
  .m-md-n2 {
    margin: -0.5rem !important;
  }
  .mt-md-n2,
  .my-md-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-md-n2,
  .mx-md-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-md-n2,
  .my-md-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-md-n2,
  .mx-md-n2,html:not([dir="rtl"]) .mfs-md-n2 {
    margin-left: -0.5rem !important;
  }
  *[dir="rtl"] .mfs-md-n2,html:not([dir="rtl"]) .mfe-md-n2 {
    margin-right: -0.5rem !important;
  }
  *[dir="rtl"] .mfe-md-n2 {
    margin-left: -0.5rem !important;
  }
  .m-md-n3 {
    margin: -1rem !important;
  }
  .mt-md-n3,
  .my-md-n3 {
    margin-top: -1rem !important;
  }
  .mr-md-n3,
  .mx-md-n3 {
    margin-right: -1rem !important;
  }
  .mb-md-n3,
  .my-md-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-md-n3,
  .mx-md-n3,html:not([dir="rtl"]) .mfs-md-n3 {
    margin-left: -1rem !important;
  }
  *[dir="rtl"] .mfs-md-n3,html:not([dir="rtl"]) .mfe-md-n3 {
    margin-right: -1rem !important;
  }
  *[dir="rtl"] .mfe-md-n3 {
    margin-left: -1rem !important;
  }
  .m-md-n4 {
    margin: -1.5rem !important;
  }
  .mt-md-n4,
  .my-md-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-md-n4,
  .mx-md-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-md-n4,
  .my-md-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-md-n4,
  .mx-md-n4,html:not([dir="rtl"]) .mfs-md-n4 {
    margin-left: -1.5rem !important;
  }
  *[dir="rtl"] .mfs-md-n4,html:not([dir="rtl"]) .mfe-md-n4 {
    margin-right: -1.5rem !important;
  }
  *[dir="rtl"] .mfe-md-n4 {
    margin-left: -1.5rem !important;
  }
  .m-md-n5 {
    margin: -3rem !important;
  }
  .mt-md-n5,
  .my-md-n5 {
    margin-top: -3rem !important;
  }
  .mr-md-n5,
  .mx-md-n5 {
    margin-right: -3rem !important;
  }
  .mb-md-n5,
  .my-md-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-md-n5,
  .mx-md-n5,html:not([dir="rtl"]) .mfs-md-n5 {
    margin-left: -3rem !important;
  }
  *[dir="rtl"] .mfs-md-n5,html:not([dir="rtl"]) .mfe-md-n5 {
    margin-right: -3rem !important;
  }
  *[dir="rtl"] .mfe-md-n5 {
    margin-left: -3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important;
  }
  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important;
  }
  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important;
  }
  .ml-md-auto,
  .mx-md-auto,html:not([dir="rtl"]) .mfs-md-auto {
    margin-left: auto !important;
  }
  *[dir="rtl"] .mfs-md-auto,html:not([dir="rtl"]) .mfe-md-auto {
    margin-right: auto !important;
  }
  *[dir="rtl"] .mfe-md-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important;
  }
  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important;
  }
  .mr-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important;
  }
  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important;
  }
  .ml-lg-0,
  .mx-lg-0,html:not([dir="rtl"]) .mfs-lg-0 {
    margin-left: 0 !important;
  }
  *[dir="rtl"] .mfs-lg-0,html:not([dir="rtl"]) .mfe-lg-0 {
    margin-right: 0 !important;
  }
  *[dir="rtl"] .mfe-lg-0 {
    margin-left: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .mt-lg-1,
  .my-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mr-lg-1,
  .mx-lg-1 {
    margin-right: 0.25rem !important;
  }
  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-lg-1,
  .mx-lg-1,html:not([dir="rtl"]) .mfs-lg-1 {
    margin-left: 0.25rem !important;
  }
  *[dir="rtl"] .mfs-lg-1,html:not([dir="rtl"]) .mfe-lg-1 {
    margin-right: 0.25rem !important;
  }
  *[dir="rtl"] .mfe-lg-1 {
    margin-left: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .mt-lg-2,
  .my-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mr-lg-2,
  .mx-lg-2 {
    margin-right: 0.5rem !important;
  }
  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-lg-2,
  .mx-lg-2,html:not([dir="rtl"]) .mfs-lg-2 {
    margin-left: 0.5rem !important;
  }
  *[dir="rtl"] .mfs-lg-2,html:not([dir="rtl"]) .mfe-lg-2 {
    margin-right: 0.5rem !important;
  }
  *[dir="rtl"] .mfe-lg-2 {
    margin-left: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .mt-lg-3,
  .my-lg-3 {
    margin-top: 1rem !important;
  }
  .mr-lg-3,
  .mx-lg-3 {
    margin-right: 1rem !important;
  }
  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 1rem !important;
  }
  .ml-lg-3,
  .mx-lg-3,html:not([dir="rtl"]) .mfs-lg-3 {
    margin-left: 1rem !important;
  }
  *[dir="rtl"] .mfs-lg-3,html:not([dir="rtl"]) .mfe-lg-3 {
    margin-right: 1rem !important;
  }
  *[dir="rtl"] .mfe-lg-3 {
    margin-left: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .mt-lg-4,
  .my-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mr-lg-4,
  .mx-lg-4 {
    margin-right: 1.5rem !important;
  }
  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-lg-4,
  .mx-lg-4,html:not([dir="rtl"]) .mfs-lg-4 {
    margin-left: 1.5rem !important;
  }
  *[dir="rtl"] .mfs-lg-4,html:not([dir="rtl"]) .mfe-lg-4 {
    margin-right: 1.5rem !important;
  }
  *[dir="rtl"] .mfe-lg-4 {
    margin-left: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .mt-lg-5,
  .my-lg-5 {
    margin-top: 3rem !important;
  }
  .mr-lg-5,
  .mx-lg-5 {
    margin-right: 3rem !important;
  }
  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 3rem !important;
  }
  .ml-lg-5,
  .mx-lg-5,html:not([dir="rtl"]) .mfs-lg-5 {
    margin-left: 3rem !important;
  }
  *[dir="rtl"] .mfs-lg-5,html:not([dir="rtl"]) .mfe-lg-5 {
    margin-right: 3rem !important;
  }
  *[dir="rtl"] .mfe-lg-5 {
    margin-left: 3rem !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important;
  }
  .pr-lg-0,
  .px-lg-0 {
    padding-right: 0 !important;
  }
  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important;
  }
  .pl-lg-0,
  .px-lg-0,html:not([dir="rtl"]) .pfs-lg-0 {
    padding-left: 0 !important;
  }
  *[dir="rtl"] .pfs-lg-0,html:not([dir="rtl"]) .pfe-lg-0 {
    padding-right: 0 !important;
  }
  *[dir="rtl"] .pfe-lg-0 {
    padding-left: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .pt-lg-1,
  .py-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pr-lg-1,
  .px-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-lg-1,
  .px-lg-1,html:not([dir="rtl"]) .pfs-lg-1 {
    padding-left: 0.25rem !important;
  }
  *[dir="rtl"] .pfs-lg-1,html:not([dir="rtl"]) .pfe-lg-1 {
    padding-right: 0.25rem !important;
  }
  *[dir="rtl"] .pfe-lg-1 {
    padding-left: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .pt-lg-2,
  .py-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pr-lg-2,
  .px-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-lg-2,
  .px-lg-2,html:not([dir="rtl"]) .pfs-lg-2 {
    padding-left: 0.5rem !important;
  }
  *[dir="rtl"] .pfs-lg-2,html:not([dir="rtl"]) .pfe-lg-2 {
    padding-right: 0.5rem !important;
  }
  *[dir="rtl"] .pfe-lg-2 {
    padding-left: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .pt-lg-3,
  .py-lg-3 {
    padding-top: 1rem !important;
  }
  .pr-lg-3,
  .px-lg-3 {
    padding-right: 1rem !important;
  }
  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pl-lg-3,
  .px-lg-3,html:not([dir="rtl"]) .pfs-lg-3 {
    padding-left: 1rem !important;
  }
  *[dir="rtl"] .pfs-lg-3,html:not([dir="rtl"]) .pfe-lg-3 {
    padding-right: 1rem !important;
  }
  *[dir="rtl"] .pfe-lg-3 {
    padding-left: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .pt-lg-4,
  .py-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pr-lg-4,
  .px-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-lg-4,
  .px-lg-4,html:not([dir="rtl"]) .pfs-lg-4 {
    padding-left: 1.5rem !important;
  }
  *[dir="rtl"] .pfs-lg-4,html:not([dir="rtl"]) .pfe-lg-4 {
    padding-right: 1.5rem !important;
  }
  *[dir="rtl"] .pfe-lg-4 {
    padding-left: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .pt-lg-5,
  .py-lg-5 {
    padding-top: 3rem !important;
  }
  .pr-lg-5,
  .px-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 3rem !important;
  }
  .pl-lg-5,
  .px-lg-5,html:not([dir="rtl"]) .pfs-lg-5 {
    padding-left: 3rem !important;
  }
  *[dir="rtl"] .pfs-lg-5,html:not([dir="rtl"]) .pfe-lg-5 {
    padding-right: 3rem !important;
  }
  *[dir="rtl"] .pfe-lg-5 {
    padding-left: 3rem !important;
  }
  .m-lg-n1 {
    margin: -0.25rem !important;
  }
  .mt-lg-n1,
  .my-lg-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-lg-n1,
  .mx-lg-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-lg-n1,
  .my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-lg-n1,
  .mx-lg-n1,html:not([dir="rtl"]) .mfs-lg-n1 {
    margin-left: -0.25rem !important;
  }
  *[dir="rtl"] .mfs-lg-n1,html:not([dir="rtl"]) .mfe-lg-n1 {
    margin-right: -0.25rem !important;
  }
  *[dir="rtl"] .mfe-lg-n1 {
    margin-left: -0.25rem !important;
  }
  .m-lg-n2 {
    margin: -0.5rem !important;
  }
  .mt-lg-n2,
  .my-lg-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-lg-n2,
  .mx-lg-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-lg-n2,
  .my-lg-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-lg-n2,
  .mx-lg-n2,html:not([dir="rtl"]) .mfs-lg-n2 {
    margin-left: -0.5rem !important;
  }
  *[dir="rtl"] .mfs-lg-n2,html:not([dir="rtl"]) .mfe-lg-n2 {
    margin-right: -0.5rem !important;
  }
  *[dir="rtl"] .mfe-lg-n2 {
    margin-left: -0.5rem !important;
  }
  .m-lg-n3 {
    margin: -1rem !important;
  }
  .mt-lg-n3,
  .my-lg-n3 {
    margin-top: -1rem !important;
  }
  .mr-lg-n3,
  .mx-lg-n3 {
    margin-right: -1rem !important;
  }
  .mb-lg-n3,
  .my-lg-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-lg-n3,
  .mx-lg-n3,html:not([dir="rtl"]) .mfs-lg-n3 {
    margin-left: -1rem !important;
  }
  *[dir="rtl"] .mfs-lg-n3,html:not([dir="rtl"]) .mfe-lg-n3 {
    margin-right: -1rem !important;
  }
  *[dir="rtl"] .mfe-lg-n3 {
    margin-left: -1rem !important;
  }
  .m-lg-n4 {
    margin: -1.5rem !important;
  }
  .mt-lg-n4,
  .my-lg-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-lg-n4,
  .mx-lg-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-lg-n4,
  .my-lg-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-lg-n4,
  .mx-lg-n4,html:not([dir="rtl"]) .mfs-lg-n4 {
    margin-left: -1.5rem !important;
  }
  *[dir="rtl"] .mfs-lg-n4,html:not([dir="rtl"]) .mfe-lg-n4 {
    margin-right: -1.5rem !important;
  }
  *[dir="rtl"] .mfe-lg-n4 {
    margin-left: -1.5rem !important;
  }
  .m-lg-n5 {
    margin: -3rem !important;
  }
  .mt-lg-n5,
  .my-lg-n5 {
    margin-top: -3rem !important;
  }
  .mr-lg-n5,
  .mx-lg-n5 {
    margin-right: -3rem !important;
  }
  .mb-lg-n5,
  .my-lg-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-lg-n5,
  .mx-lg-n5,html:not([dir="rtl"]) .mfs-lg-n5 {
    margin-left: -3rem !important;
  }
  *[dir="rtl"] .mfs-lg-n5,html:not([dir="rtl"]) .mfe-lg-n5 {
    margin-right: -3rem !important;
  }
  *[dir="rtl"] .mfe-lg-n5 {
    margin-left: -3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important;
  }
  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important;
  }
  .ml-lg-auto,
  .mx-lg-auto,html:not([dir="rtl"]) .mfs-lg-auto {
    margin-left: auto !important;
  }
  *[dir="rtl"] .mfs-lg-auto,html:not([dir="rtl"]) .mfe-lg-auto {
    margin-right: auto !important;
  }
  *[dir="rtl"] .mfe-lg-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important;
  }
  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important;
  }
  .mr-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important;
  }
  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important;
  }
  .ml-xl-0,
  .mx-xl-0,html:not([dir="rtl"]) .mfs-xl-0 {
    margin-left: 0 !important;
  }
  *[dir="rtl"] .mfs-xl-0,html:not([dir="rtl"]) .mfe-xl-0 {
    margin-right: 0 !important;
  }
  *[dir="rtl"] .mfe-xl-0 {
    margin-left: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .mt-xl-1,
  .my-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mr-xl-1,
  .mx-xl-1 {
    margin-right: 0.25rem !important;
  }
  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .ml-xl-1,
  .mx-xl-1,html:not([dir="rtl"]) .mfs-xl-1 {
    margin-left: 0.25rem !important;
  }
  *[dir="rtl"] .mfs-xl-1,html:not([dir="rtl"]) .mfe-xl-1 {
    margin-right: 0.25rem !important;
  }
  *[dir="rtl"] .mfe-xl-1 {
    margin-left: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .mt-xl-2,
  .my-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mr-xl-2,
  .mx-xl-2 {
    margin-right: 0.5rem !important;
  }
  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .ml-xl-2,
  .mx-xl-2,html:not([dir="rtl"]) .mfs-xl-2 {
    margin-left: 0.5rem !important;
  }
  *[dir="rtl"] .mfs-xl-2,html:not([dir="rtl"]) .mfe-xl-2 {
    margin-right: 0.5rem !important;
  }
  *[dir="rtl"] .mfe-xl-2 {
    margin-left: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .mt-xl-3,
  .my-xl-3 {
    margin-top: 1rem !important;
  }
  .mr-xl-3,
  .mx-xl-3 {
    margin-right: 1rem !important;
  }
  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 1rem !important;
  }
  .ml-xl-3,
  .mx-xl-3,html:not([dir="rtl"]) .mfs-xl-3 {
    margin-left: 1rem !important;
  }
  *[dir="rtl"] .mfs-xl-3,html:not([dir="rtl"]) .mfe-xl-3 {
    margin-right: 1rem !important;
  }
  *[dir="rtl"] .mfe-xl-3 {
    margin-left: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .mt-xl-4,
  .my-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mr-xl-4,
  .mx-xl-4 {
    margin-right: 1.5rem !important;
  }
  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .ml-xl-4,
  .mx-xl-4,html:not([dir="rtl"]) .mfs-xl-4 {
    margin-left: 1.5rem !important;
  }
  *[dir="rtl"] .mfs-xl-4,html:not([dir="rtl"]) .mfe-xl-4 {
    margin-right: 1.5rem !important;
  }
  *[dir="rtl"] .mfe-xl-4 {
    margin-left: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .mt-xl-5,
  .my-xl-5 {
    margin-top: 3rem !important;
  }
  .mr-xl-5,
  .mx-xl-5 {
    margin-right: 3rem !important;
  }
  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 3rem !important;
  }
  .ml-xl-5,
  .mx-xl-5,html:not([dir="rtl"]) .mfs-xl-5 {
    margin-left: 3rem !important;
  }
  *[dir="rtl"] .mfs-xl-5,html:not([dir="rtl"]) .mfe-xl-5 {
    margin-right: 3rem !important;
  }
  *[dir="rtl"] .mfe-xl-5 {
    margin-left: 3rem !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important;
  }
  .pr-xl-0,
  .px-xl-0 {
    padding-right: 0 !important;
  }
  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important;
  }
  .pl-xl-0,
  .px-xl-0,html:not([dir="rtl"]) .pfs-xl-0 {
    padding-left: 0 !important;
  }
  *[dir="rtl"] .pfs-xl-0,html:not([dir="rtl"]) .pfe-xl-0 {
    padding-right: 0 !important;
  }
  *[dir="rtl"] .pfe-xl-0 {
    padding-left: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .pt-xl-1,
  .py-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pr-xl-1,
  .px-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pl-xl-1,
  .px-xl-1,html:not([dir="rtl"]) .pfs-xl-1 {
    padding-left: 0.25rem !important;
  }
  *[dir="rtl"] .pfs-xl-1,html:not([dir="rtl"]) .pfe-xl-1 {
    padding-right: 0.25rem !important;
  }
  *[dir="rtl"] .pfe-xl-1 {
    padding-left: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .pt-xl-2,
  .py-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pr-xl-2,
  .px-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pl-xl-2,
  .px-xl-2,html:not([dir="rtl"]) .pfs-xl-2 {
    padding-left: 0.5rem !important;
  }
  *[dir="rtl"] .pfs-xl-2,html:not([dir="rtl"]) .pfe-xl-2 {
    padding-right: 0.5rem !important;
  }
  *[dir="rtl"] .pfe-xl-2 {
    padding-left: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .pt-xl-3,
  .py-xl-3 {
    padding-top: 1rem !important;
  }
  .pr-xl-3,
  .px-xl-3 {
    padding-right: 1rem !important;
  }
  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pl-xl-3,
  .px-xl-3,html:not([dir="rtl"]) .pfs-xl-3 {
    padding-left: 1rem !important;
  }
  *[dir="rtl"] .pfs-xl-3,html:not([dir="rtl"]) .pfe-xl-3 {
    padding-right: 1rem !important;
  }
  *[dir="rtl"] .pfe-xl-3 {
    padding-left: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .pt-xl-4,
  .py-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pr-xl-4,
  .px-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pl-xl-4,
  .px-xl-4,html:not([dir="rtl"]) .pfs-xl-4 {
    padding-left: 1.5rem !important;
  }
  *[dir="rtl"] .pfs-xl-4,html:not([dir="rtl"]) .pfe-xl-4 {
    padding-right: 1.5rem !important;
  }
  *[dir="rtl"] .pfe-xl-4 {
    padding-left: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .pt-xl-5,
  .py-xl-5 {
    padding-top: 3rem !important;
  }
  .pr-xl-5,
  .px-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 3rem !important;
  }
  .pl-xl-5,
  .px-xl-5,html:not([dir="rtl"]) .pfs-xl-5 {
    padding-left: 3rem !important;
  }
  *[dir="rtl"] .pfs-xl-5,html:not([dir="rtl"]) .pfe-xl-5 {
    padding-right: 3rem !important;
  }
  *[dir="rtl"] .pfe-xl-5 {
    padding-left: 3rem !important;
  }
  .m-xl-n1 {
    margin: -0.25rem !important;
  }
  .mt-xl-n1,
  .my-xl-n1 {
    margin-top: -0.25rem !important;
  }
  .mr-xl-n1,
  .mx-xl-n1 {
    margin-right: -0.25rem !important;
  }
  .mb-xl-n1,
  .my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .ml-xl-n1,
  .mx-xl-n1,html:not([dir="rtl"]) .mfs-xl-n1 {
    margin-left: -0.25rem !important;
  }
  *[dir="rtl"] .mfs-xl-n1,html:not([dir="rtl"]) .mfe-xl-n1 {
    margin-right: -0.25rem !important;
  }
  *[dir="rtl"] .mfe-xl-n1 {
    margin-left: -0.25rem !important;
  }
  .m-xl-n2 {
    margin: -0.5rem !important;
  }
  .mt-xl-n2,
  .my-xl-n2 {
    margin-top: -0.5rem !important;
  }
  .mr-xl-n2,
  .mx-xl-n2 {
    margin-right: -0.5rem !important;
  }
  .mb-xl-n2,
  .my-xl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .ml-xl-n2,
  .mx-xl-n2,html:not([dir="rtl"]) .mfs-xl-n2 {
    margin-left: -0.5rem !important;
  }
  *[dir="rtl"] .mfs-xl-n2,html:not([dir="rtl"]) .mfe-xl-n2 {
    margin-right: -0.5rem !important;
  }
  *[dir="rtl"] .mfe-xl-n2 {
    margin-left: -0.5rem !important;
  }
  .m-xl-n3 {
    margin: -1rem !important;
  }
  .mt-xl-n3,
  .my-xl-n3 {
    margin-top: -1rem !important;
  }
  .mr-xl-n3,
  .mx-xl-n3 {
    margin-right: -1rem !important;
  }
  .mb-xl-n3,
  .my-xl-n3 {
    margin-bottom: -1rem !important;
  }
  .ml-xl-n3,
  .mx-xl-n3,html:not([dir="rtl"]) .mfs-xl-n3 {
    margin-left: -1rem !important;
  }
  *[dir="rtl"] .mfs-xl-n3,html:not([dir="rtl"]) .mfe-xl-n3 {
    margin-right: -1rem !important;
  }
  *[dir="rtl"] .mfe-xl-n3 {
    margin-left: -1rem !important;
  }
  .m-xl-n4 {
    margin: -1.5rem !important;
  }
  .mt-xl-n4,
  .my-xl-n4 {
    margin-top: -1.5rem !important;
  }
  .mr-xl-n4,
  .mx-xl-n4 {
    margin-right: -1.5rem !important;
  }
  .mb-xl-n4,
  .my-xl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .ml-xl-n4,
  .mx-xl-n4,html:not([dir="rtl"]) .mfs-xl-n4 {
    margin-left: -1.5rem !important;
  }
  *[dir="rtl"] .mfs-xl-n4,html:not([dir="rtl"]) .mfe-xl-n4 {
    margin-right: -1.5rem !important;
  }
  *[dir="rtl"] .mfe-xl-n4 {
    margin-left: -1.5rem !important;
  }
  .m-xl-n5 {
    margin: -3rem !important;
  }
  .mt-xl-n5,
  .my-xl-n5 {
    margin-top: -3rem !important;
  }
  .mr-xl-n5,
  .mx-xl-n5 {
    margin-right: -3rem !important;
  }
  .mb-xl-n5,
  .my-xl-n5 {
    margin-bottom: -3rem !important;
  }
  .ml-xl-n5,
  .mx-xl-n5,html:not([dir="rtl"]) .mfs-xl-n5 {
    margin-left: -3rem !important;
  }
  *[dir="rtl"] .mfs-xl-n5,html:not([dir="rtl"]) .mfe-xl-n5 {
    margin-right: -3rem !important;
  }
  *[dir="rtl"] .mfe-xl-n5 {
    margin-left: -3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important;
  }
  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important;
  }
  .ml-xl-auto,
  .mx-xl-auto,html:not([dir="rtl"]) .mfs-xl-auto {
    margin-left: auto !important;
  }
  *[dir="rtl"] .mfs-xl-auto,html:not([dir="rtl"]) .mfe-xl-auto {
    margin-right: auto !important;
  }
  *[dir="rtl"] .mfe-xl-auto {
    margin-left: auto !important;
  }
}

.text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}

.text-justify {
  text-align: justify !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important;
  }
  .text-sm-right {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important;
  }
  .text-md-right {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}

@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important;
  }
  .text-lg-right {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important;
  }
  .text-xl-right {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-lighter {
  font-weight: lighter !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.font-weight-bolder {
  font-weight: bolder !important;
}

.font-italic {
  font-style: italic !important;
}

.text-white {
  color: #fff !important;
}

.text-primary {
  color: #321fdb !important;
}

a.text-primary:hover, a.text-primary:focus {
  color: #231698 !important;
}

.text-secondary {
  color: #ced2d8 !important;
}

a.text-secondary:hover, a.text-secondary:focus {
  color: #a3abb6 !important;
}

.text-success {
  color: #2eb85c !important;
}

a.text-success:hover, a.text-success:focus {
  color: #1f7b3d !important;
}

.text-info {
  color: #39f !important;
}

a.text-info:hover, a.text-info:focus {
  color: #0073e6 !important;
}

.text-warning {
  color: #f9b115 !important;
}

a.text-warning:hover, a.text-warning:focus {
  color: #bd8305 !important;
}

.text-danger {
  color: #e55353 !important;
}

a.text-danger:hover, a.text-danger:focus {
  color: #cd1f1f !important;
}

.text-light {
  color: #ebedef !important;
}

a.text-light:hover, a.text-light:focus {
  color: #c1c7cd !important;
}

.text-dark {
  color: #636f83 !important;
}

a.text-dark:hover, a.text-dark:focus {
  color: #424a57 !important;
}

.text-body {
  color: #4f5d73 !important;
}

.text-muted {
  color: #768192 !important;
}

.text-black-50 {
  color: rgba(0, 0, 21, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-break {
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}

.text-reset {
  color: inherit !important;
}

.font-xs {
  font-size: .75rem !important;
}

.font-sm {
  font-size: .85rem !important;
}

.font-lg {
  font-size: 1rem !important;
}

.font-xl {
  font-size: 1.25rem !important;
}

.font-2xl {
  font-size: 1.5rem !important;
}

.font-3xl {
  font-size: 1.75rem !important;
}

.font-4xl {
  font-size: 2rem !important;
}

.font-5xl {
  font-size: 2.5rem !important;
}

[class^="text-value"] {
  font-weight: 600;
}

.text-value-xs {
  font-size: 0.65625rem;
}

.text-value-sm {
  font-size: 0.74375rem;
}

.text-value {
  font-size: 0.875rem;
}

.text-value-lg {
  font-size: 1.3125rem;
}

.text-value-xl {
  font-size: 1.53125rem;
}

.text-white .text-muted {
  color: rgba(255, 255, 255, 0.6) !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

*[dir="rtl"] {
  direction: rtl;
  unicode-bidi: embed;
}

*[dir="rtl"] body {
  text-align: right;
}

.ie-custom-properties {
  primary: #321fdb;
  secondary: #ced2d8;
  success: #2eb85c;
  info: #39f;
  warning: #f9b115;
  danger: #e55353;
  light: #ebedef;
  dark: #636f83;
  breakpoint-xs: 0;
  breakpoint-sm: 576px;
  breakpoint-md: 768px;
  breakpoint-lg: 992px;
  breakpoint-xl: 1200px;
}

@media print {
  *,
  *::before,
  *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  a:not(.btn) {
    text-decoration: underline;
  }
  abbr[title]::after {
    content: " (" attr(title) ")";
  }
  pre {
    white-space: pre-wrap !important;
  }
  pre,
  blockquote {
    border: 1px solid #9da5b1;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  @page {
    size: a3;
  }
  body,.container {
    min-width: 992px !important;
  }
  .navbar {
    display: none;
  }
  .badge {
    border: 1px solid #000015;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
  .table-bordered th,
  .table-bordered td {
    border: 1px solid #c4c9d0 !important;
  }
  .table-dark {
    color: inherit;
  }
  .table-dark th,
  .table-dark td,
  .table-dark thead th,
  .table-dark tbody + tbody,.table .thead-dark th {
    border-color: #d8dbe0;
  }
  .table .thead-dark th {
    color: inherit;
  }
}
/*# sourceMappingURL=coreui.css.map */