<!-- Player info modal -->
<div class="modal fade" id="modPlayer" tabindex="-1" role="dialog" aria-labelledby="modPlayerTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document" style="min-width: 680px;">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="modPlayerTitle">Loading...</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body" style="min-height: 300px;">
                <div class="text-center p-3" style="height: 100px; font-size: larger;" id="modPlayerMessage">
                    <div class="txSpinner">Loading...</div>
                </div>
                <div class="row p-0 d-none" id="modPlayerContent">

                    <div class="col-3 border-right">
                        <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                            <a class="nav-link active" id="modPlayerMain-tab" data-toggle="pill" href="#modPlayerMain" role="tab" 
                                aria-controls="modPlayerMain" aria-selected="true"><i class="icon-user"></i> Info</a>
                            <a class="nav-link" id="modPlayerIDs-tab" data-toggle="pill" href="#modPlayerIDs" role="tab" 
                                aria-controls="modPlayerIDs" aria-selected="false"><i class="icon-list"></i> IDs</a>
                            <a class="nav-link" id="modPlayerHistory-tab" data-toggle="pill" href="#modPlayerHistory" role="tab" 
                                aria-controls="modPlayerHistory" aria-selected="false"><i class="icon-notebook"></i> History</a>
                            <a class="nav-link nav-link-red" id="modPlayerBan-tab" data-toggle="pill" href="#modPlayerBan" role="tab" 
                                aria-controls="modPlayerBan" aria-selected="false"><i class="icon-shield"></i> Ban</a>
                        </div>
                    </div> <!-- End Col -->

                    <div class="col-9">
                        <div class="tab-content">

                            <!-- Tab modPlayerMain -->
                            <div class="tab-pane fade show active" id="modPlayerMain" role="tabpanel" aria-labelledby="modPlayerMain-tab" style="font-size: medium;">
                                <div id="modPlayerMain-sessionTimeDiv">
                                    <strong>Session time:</strong> <span id="modPlayerMain-sessionTimeText">--</span> <br>
                                </div>
                                <strong>Play time:</strong> <span id="modPlayerMain-playTime">--</span> <br>
                                <strong>Join date:</strong> <span id="modPlayerMain-joinDate">--</span> <br>
                                <div id="modPlayerMain-lastConnectionDiv">
                                    <strong>Last connection:</strong> <span id="modPlayerMain-lastConnectionText">--</span> <br>
                                </div>

                                <strong>Whitelisted:</strong>
                                <span id="modPlayerMain-whitelisted">not yet</span>
                                <button class="btn btn-inline-sm btn-success" type="button" id="modPlayerMain-whitelistAddBtn">
                                    ADD WL
                                </button>
                                <button class="btn btn-inline-sm btn-dark" type="button" id="modPlayerMain-whitelistRemoveBtn">
                                    REMOVE WL
                                </button>
                                <br>

                                <strong>Log:</strong>
                                <span id="modPlayerMain-logCountBans" class="text-danger">1 ban</span>,
                                <span id="modPlayerMain-logCountWarns" class="text-warning">2 warns</span>
                                <button class="btn btn-inline-sm btn-dark" type="button" id="modPlayerMain-logDetailsBtn">
                                    DETAILS
                                </button>
                                <br>

                                <div class="form-group row m-0 mt-2">
                                    <label for="modPlayerFormNotes">
                                        <strong>Notes:</strong>
                                        <span id="modPlayerMain-notesLog" class="text-muted">
                                            --
                                        </span>
                                    </label>
                                    <textarea class="form-control" id="modPlayerMain-notes" rows="3" 
                                        placeholder="Insert notes here regarding this player."></textarea>
                                </div>
                            </div>

                            <!-- Tab modPlayerIDs -->
                            <div class="tab-pane fade" id="modPlayerIDs" role="tabpanel" aria-labelledby="modPlayerIDs-tab">
                                <style>
                                    /* NOTE: this really shouldn't be here, but it's very temporary */
                                    .scrollBox {
                                        max-height: 55vh;
                                        overflow-y: scroll;
                                        padding-right: 10px;
                                    }
                                    .idsText {
                                        border-radius: 4px;
                                        padding: 6px 8px;
                                        margin-bottom: 7px;
                                        display: flex;
                                        align-items: center;
                                        font-family: monospace;
                                        font-size: medium;
                                        letter-spacing: 0.05rem;
                                    }
                                </style>
                                
                                <div class="thin-scroll scrollBox">
                                    <div class="mb-3">
                                        <h5>Current Identifiers:</h5>
                                        <div id="modPlayerIDs-currList" >
                                            Loading...
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <h5>Previously Used Identifiers:</h5>
                                        <div id="modPlayerIDs-oldList">
                                            Loading...
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <h5>Hardware IDs:</h5>
                                        <div id="modPlayerIDs-hwidsList">
                                            Loading...
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tab modPlayerHistory -->
                            <div class="tab-pane fade" id="modPlayerHistory" role="tabpanel" aria-labelledby="modPlayerHistory-tab">
                                <h5>Related History:</h5>
                                <div class="list-group list-group-accent" id="modPlayerHistory-log">
                                    loading...
                                </div>
                            </div>

                            <!-- Tab modPlayerBan -->
                            <div class="tab-pane fade" id="modPlayerBan" role="tabpanel" aria-labelledby="modPlayerBan-tab">
                                <div class="form-group row">
                                    <label for="modPlayerBan-reason" class="col-sm-3 col-form-label">
                                        Reason
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="modPlayerBan-reason" maxlength="256"
                                            placeholder="doing bad things :(">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label for="modPlayerBan-durationSelect" class="col-sm-3 col-form-label">
                                        Duration
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="col-sm-9">
                                        <select class="form-control" id="modPlayerBan-durationSelect">
                                            <option value="2 hours">2 hours</option>
                                            <option value="8 hours">8 hours</option>
                                            <option value="1 day">1 day</option>
                                            <option value="2 days" selected>2 days</option>
                                            <option value="1 week">1 week</option>
                                            <option value="2 weeks">2 weeks</option>
                                            <option value="permanent" class="font-weight-bold text-danger">permanent</option>
                                            <option value="custom" class="font-weight-bold">custom</option>
                                        </select>
                                        <div class="row mt-2">
                                            <div class="col-sm-3 pr-0">
                                                <input type="number" class="form-control" id="modPlayerBan-durationMultiplier" 
                                                    maxlength="3" placeholder="7" disabled>
                                            </div>
                                            <div class="col-sm-9 pl-2">
                                                <select class="form-control" id="modPlayerBan-durationUnit" disabled>
                                                    <option value="hours">hours</option>
                                                    <option value="days" selected>days</option>
                                                    <option value="weeks">weeks</option>
                                                    <option value="months">months</option>
                                                </select>
                                            </div>
                                        </div>
                                        <span class="form-text text-muted">
                                            <b>Note:</b> The ban will be applied to all identifiers.
                                        </span>
                                    </div>
                                </div>
            
                                <div class="text-center mt-4">
                                    <button class="btn btn-sm btn-danger" type="submit" 
                                        id="modPlayerButtons-ban" onclick="banPlayer()">
                                        <i class="icons cui-check"></i> Ban Player
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div> <!-- End Col -->

                </div> <!-- End Row -->
            </div> <!-- End Modal Body -->
            <div class="modal-footer align-items-end <%= (!isWebInterface ? 'd-none' : 'd-flex') %>">
                <button type="button" class="btn btn-outline-dark"
                    id="modPlayerButtons-message" onclick="messagePlayer()">
                    <i class="icon-speech"></i> DM
                </button>
                <button type="button" class="btn btn-outline-dark"
                    id="modPlayerButtons-kick" onclick="kickPlayer()">
                    <i class="icon-ban"></i> Kick
                </button>
                <button type="button" class="btn btn-outline-warning"
                    id="modPlayerButtons-warn" onclick="warnPlayer()">
                    <i class="icon-exclamation"></i> Warn
                </button>
            </div>
        </div>
    </div>
</div>
